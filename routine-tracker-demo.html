<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Routine & Results Tracker | Tech Talk</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- Welcome Screen -->
    <div id="welcomeScreen" class="min-h-screen flex items-center justify-center p-4">
        <div class="max-w-md w-full">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="text-6xl mb-4">🧱</div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    Digital Routine & Results Tracker
                </h1>
                <p class="text-gray-600 text-lg mb-2">
                    Track your daily growth with intention
                </p>
                <div class="flex items-center justify-center space-x-2 text-sm text-indigo-600">
                    <span class="font-medium">by Tech Talk</span>
                    <span>💬</span>
                </div>
            </div>

            <!-- Description -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">
                    What we're building here 💻✅
                </h2>
                <div class="space-y-3 text-gray-600">
                    <div class="flex items-start space-x-2">
                        <span class="text-indigo-500 mt-1">•</span>
                        <span>Type in your name</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <span class="text-indigo-500 mt-1">•</span>
                        <span>Tick what you've done for the day (Prayer, Study, Hygiene, Work, etc.)</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <span class="text-indigo-500 mt-1">•</span>
                        <span>Submit, and it saves your progress</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <span class="text-indigo-500 mt-1">•</span>
                        <span>Come back anytime before the day ends to update it</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <span class="text-indigo-500 mt-1">•</span>
                        <span>System resets at midnight, but keeps a history of your growth</span>
                    </div>
                </div>
            </div>

            <!-- Login Form -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    Ready to track your growth? 📊✨
                </h3>
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Your Name
                        </label>
                        <input
                            type="text"
                            id="name"
                            placeholder="Enter your name..."
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors"
                            required
                        />
                    </div>
                    <button
                        type="submit"
                        class="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
                    >
                        Start Tracking 🔥
                    </button>
                </form>
            </div>

            <!-- Footer -->
            <div class="text-center mt-6 text-gray-500 text-sm">
                <p class="mb-3">No login stress. No judgment. Just you, your goals, and your growth.</p>
                <div class="flex items-center justify-center space-x-4 text-xs mb-4">
                    <span class="flex items-center space-x-1">
                        <span>🔒</span>
                        <span>Secure</span>
                    </span>
                    <span class="flex items-center space-x-1">
                        <span>💾</span>
                        <span>Auto-save</span>
                    </span>
                    <span class="flex items-center space-x-1">
                        <span>📊</span>
                        <span>Track Growth</span>
                    </span>
                </div>

                <!-- Brand Credit -->
                <div class="border-t border-gray-200 pt-4">
                    <div class="flex items-center justify-center space-x-2 text-xs">
                        <span class="text-gray-400">Powered by</span>
                        <div class="flex items-center space-x-1">
                            <span class="text-indigo-600 font-semibold">Tech Talk</span>
                            <span class="text-indigo-500">💬</span>
                        </div>
                    </div>
                    <p class="text-gray-400 text-xs mt-1">Building digital solutions for growth</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tracker Screen -->
    <div id="trackerScreen" class="min-h-screen p-4 hidden">
        <div class="max-w-2xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h1 id="welcomeMessage" class="text-2xl font-bold text-gray-900">
                            Welcome back! 👋
                        </h1>
                        <p id="currentDate" class="text-gray-600"></p>
                    </div>
                    <button
                        id="logoutBtn"
                        class="text-gray-500 hover:text-gray-700 text-sm underline"
                    >
                        Switch User
                    </button>
                </div>
                
                <!-- Progress Summary -->
                <div class="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">Today's Progress</p>
                            <p id="progressCount" class="text-2xl font-bold text-indigo-600">0/8</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-600">Completion Rate</p>
                            <p id="completionRate" class="text-2xl font-bold text-indigo-600">0%</p>
                        </div>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="mt-4">
                        <div class="bg-gray-200 rounded-full h-2">
                            <div 
                                id="progressBar"
                                class="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                                style="width: 0%"
                            ></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Routine Items -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-6">
                    Daily Routine Checklist ✅
                </h2>
                
                <div id="routineItems" class="space-y-3">
                    <!-- Items will be populated by JavaScript -->
                </div>
                
                <!-- Motivational Message -->
                <div class="text-center mt-6 text-gray-600">
                    <p id="motivationalMessage" class="text-sm">
                        🌱 Every step counts. You've got this!
                    </p>
                    <p class="text-xs mt-2 mb-4">
                        Progress auto-saves • Resets at midnight • Your growth is tracked
                    </p>

                    <!-- Brand Credit -->
                    <div class="border-t border-gray-200 pt-4 mt-4">
                        <div class="flex items-center justify-center space-x-2 text-xs">
                            <span class="text-gray-400">Crafted with ❤️ by</span>
                            <div class="flex items-center space-x-1">
                                <span class="text-indigo-600 font-semibold">Tech Talk</span>
                                <span class="text-indigo-500">💬</span>
                            </div>
                        </div>
                        <p class="text-gray-400 text-xs mt-1">Empowering your digital growth journey</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Default routine items
        const routineItems = [
            { id: 'prayer', name: 'Prayer', icon: '🙏', description: 'Daily spiritual practice and reflection' },
            { id: 'study', name: 'Study', icon: '📚', description: 'Learning, reading, or skill development' },
            { id: 'hygiene', name: 'Hygiene', icon: '🧼', description: 'Personal care and cleanliness' },
            { id: 'work', name: 'Work', icon: '💼', description: 'Professional tasks and responsibilities' },
            { id: 'exercise', name: 'Exercise', icon: '💪', description: 'Physical activity and fitness' },
            { id: 'nutrition', name: 'Nutrition', icon: '🥗', description: 'Healthy eating and meal planning' },
            { id: 'reflection', name: 'Reflection', icon: '🤔', description: 'Daily journaling or self-reflection' },
            { id: 'connection', name: 'Connection', icon: '👥', description: 'Meaningful social interactions' }
        ];

        let currentUser = null;
        let completedItems = [];

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is already logged in
            const savedUser = localStorage.getItem('routine_tracker_current_user');
            if (savedUser) {
                currentUser = savedUser;
                loadTracker();
            }

            // Set up event listeners
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
            document.getElementById('logoutBtn').addEventListener('click', handleLogout);
        });

        function handleLogin(e) {
            e.preventDefault();
            const name = document.getElementById('name').value.trim();
            if (!name) return;

            currentUser = name;
            localStorage.setItem('routine_tracker_current_user', name);
            loadTracker();
        }

        function handleLogout() {
            localStorage.removeItem('routine_tracker_current_user');
            currentUser = null;
            completedItems = [];
            document.getElementById('welcomeScreen').classList.remove('hidden');
            document.getElementById('trackerScreen').classList.add('hidden');
            document.getElementById('name').value = '';
        }

        function loadTracker() {
            // Load saved progress for today
            const today = new Date().toISOString().split('T')[0];
            const savedProgress = localStorage.getItem(`routine_progress_${currentUser}_${today}`);
            if (savedProgress) {
                completedItems = JSON.parse(savedProgress);
            } else {
                completedItems = [];
            }

            // Update UI
            document.getElementById('welcomeMessage').textContent = `Welcome back, ${currentUser}! 👋`;
            document.getElementById('currentDate').textContent = new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });

            renderRoutineItems();
            updateProgress();

            // Show tracker screen
            document.getElementById('welcomeScreen').classList.add('hidden');
            document.getElementById('trackerScreen').classList.remove('hidden');
        }

        function renderRoutineItems() {
            const container = document.getElementById('routineItems');
            container.innerHTML = '';

            routineItems.forEach(item => {
                const isCompleted = completedItems.includes(item.id);
                const itemElement = document.createElement('div');
                itemElement.className = `flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                    isCompleted
                        ? 'border-green-200 bg-green-50'
                        : 'border-gray-200 bg-gray-50 hover:border-indigo-200 hover:bg-indigo-50'
                }`;
                
                itemElement.innerHTML = `
                    <div class="flex items-center space-x-4 flex-1">
                        <div class="text-2xl">${item.icon}</div>
                        <div class="flex-1">
                            <h3 class="font-medium ${isCompleted ? 'text-green-800' : 'text-gray-800'}">${item.name}</h3>
                            <p class="text-sm ${isCompleted ? 'text-green-600' : 'text-gray-600'}">${item.description}</p>
                        </div>
                    </div>
                    <div class="w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                        isCompleted ? 'border-green-500 bg-green-500' : 'border-gray-300'
                    }">
                        ${isCompleted ? '<svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>' : ''}
                    </div>
                `;

                itemElement.addEventListener('click', () => toggleItem(item.id));
                container.appendChild(itemElement);
            });
        }

        function toggleItem(itemId) {
            if (completedItems.includes(itemId)) {
                completedItems = completedItems.filter(id => id !== itemId);
            } else {
                completedItems.push(itemId);
            }

            // Save progress
            const today = new Date().toISOString().split('T')[0];
            localStorage.setItem(`routine_progress_${currentUser}_${today}`, JSON.stringify(completedItems));

            // Update UI
            renderRoutineItems();
            updateProgress();
        }

        function updateProgress() {
            const completed = completedItems.length;
            const total = routineItems.length;
            const percentage = Math.round((completed / total) * 100);

            document.getElementById('progressCount').textContent = `${completed}/${total}`;
            document.getElementById('completionRate').textContent = `${percentage}%`;
            document.getElementById('progressBar').style.width = `${percentage}%`;

            // Update motivational message
            let message = '';
            if (percentage === 100) {
                message = "🎉 Amazing! You've completed all your routines today!";
            } else if (percentage >= 75) {
                message = "🔥 You're doing great! Keep it up!";
            } else if (percentage >= 50) {
                message = "💪 Good progress! You're halfway there!";
            } else {
                message = "🌱 Every step counts. You've got this!";
            }
            document.getElementById('motivationalMessage').textContent = message;
        }
    </script>
</body>
</html>
