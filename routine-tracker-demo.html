<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Digital Routine & Results Tracker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- Welcome Screen -->
    <div id="welcomeScreen" class="min-h-screen flex items-center justify-center p-4">
        <div class="max-w-md w-full">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="text-6xl mb-4">🧱</div>
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    Digital Routine & Results Tracker
                </h1>
                <p class="text-gray-600 text-lg">
                    Track your daily growth with intention
                </p>
            </div>

            <!-- Description -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">
                    What we're building here 💻✅
                </h2>
                <div class="space-y-3 text-gray-600">
                    <div class="flex items-start space-x-2">
                        <span class="text-indigo-500 mt-1">•</span>
                        <span>Type in your name</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <span class="text-indigo-500 mt-1">•</span>
                        <span>Tick what you've done for the day (Prayer, Study, Hygiene, Work, etc.)</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <span class="text-indigo-500 mt-1">•</span>
                        <span>Submit, and it saves your progress</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <span class="text-indigo-500 mt-1">•</span>
                        <span>Come back anytime before the day ends to update it</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <span class="text-indigo-500 mt-1">•</span>
                        <span>System resets at midnight, but keeps a history of your growth</span>
                    </div>
                </div>
            </div>

            <!-- Login Form -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    Ready to track your growth? 📊✨
                </h3>
                <form id="loginForm" class="space-y-4">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Your Name
                        </label>
                        <input
                            type="text"
                            id="name"
                            placeholder="Enter your name..."
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors"
                            required
                        />
                    </div>
                    <button
                        type="submit"
                        class="w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
                    >
                        Start Tracking 🔥
                    </button>
                </form>
            </div>

            <!-- Footer -->
            <div class="text-center mt-6 text-gray-500 text-sm">
                <p>No login stress. No judgment. Just you, your goals, and your growth.</p>
            </div>
        </div>
    </div>

    <!-- Tracker Screen -->
    <div id="trackerScreen" class="min-h-screen p-4 hidden">
        <div class="max-w-2xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <h1 id="welcomeMessage" class="text-2xl font-bold text-gray-900">
                            Welcome back! 👋
                        </h1>
                        <p id="currentDate" class="text-gray-600"></p>
                    </div>
                    <button
                        id="logoutBtn"
                        class="text-gray-500 hover:text-gray-700 text-sm underline"
                    >
                        Switch User
                    </button>
                </div>
                
                <!-- Progress Summary -->
                <div class="bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm text-gray-600">Today's Progress</p>
                            <p id="progressCount" class="text-2xl font-bold text-indigo-600">0/8</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-600">Completion Rate</p>
                            <p id="completionRate" class="text-2xl font-bold text-indigo-600">0%</p>
                        </div>
                    </div>
                    
                    <!-- Progress Bar -->
                    <div class="mt-4">
                        <div class="bg-gray-200 rounded-full h-2">
                            <div 
                                id="progressBar"
                                class="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                                style="width: 0%"
                            ></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Routine Items -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-6">
                    Daily Routine Checklist ✅
                </h2>
                
                <div id="routineItems" class="space-y-3">
                    <!-- Items will be populated by JavaScript -->
                </div>
                
                <!-- Motivational Message -->
                <div class="text-center mt-6 text-gray-600">
                    <p id="motivationalMessage" class="text-sm">
                        🌱 Every step counts. You've got this!
                    </p>
                    <p class="text-xs mt-2 mb-4">
                        Progress auto-saves • Resets at midnight • Your growth is tracked
                    </p>

                    <!-- Same-day Update Info -->
                    <div class="text-center text-xs text-gray-500">
                        <p>✨ You can update your progress anytime before the day ends</p>
                        <p>📊 Click the info button (ⓘ) next to any task for detailed guidance</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Default routine items with detailed info
        const routineItems = [
            {
                id: 'prayer',
                name: 'Prayer',
                icon: '🙏',
                description: 'Daily spiritual practice and reflection',
                detailedInfo: {
                    purpose: 'Connect with your spiritual side and find inner peace through daily prayer and meditation.',
                    benefits: ['Reduces stress and anxiety', 'Provides mental clarity and focus', 'Strengthens spiritual connection'],
                    tips: ['Set aside a quiet time each day', 'Find a comfortable, peaceful space', 'Start with just 5-10 minutes'],
                    timeRecommendation: '10-30 minutes'
                }
            },
            {
                id: 'study',
                name: 'Study',
                icon: '📚',
                description: 'Learning, reading, or skill development',
                detailedInfo: {
                    purpose: 'Continuous learning and personal development through reading, courses, or skill practice.',
                    benefits: ['Expands knowledge and skills', 'Improves cognitive function', 'Enhances career prospects'],
                    tips: ['Choose topics that interest you', 'Set specific learning goals', 'Take notes and review regularly'],
                    timeRecommendation: '30-60 minutes'
                }
            },
            {
                id: 'hygiene',
                name: 'Hygiene',
                icon: '🧼',
                description: 'Personal care and cleanliness',
                detailedInfo: {
                    purpose: 'Maintain physical health and personal presentation through proper hygiene practices.',
                    benefits: ['Prevents illness and infections', 'Boosts self-confidence', 'Shows respect for others'],
                    tips: ['Brush teeth twice daily', 'Shower regularly', 'Keep nails clean and trimmed'],
                    timeRecommendation: '20-45 minutes total'
                }
            },
            {
                id: 'work',
                name: 'Work',
                icon: '💼',
                description: 'Professional tasks and responsibilities',
                detailedInfo: {
                    purpose: 'Focus on productive work activities that contribute to your career and financial goals.',
                    benefits: ['Builds career advancement', 'Provides financial stability', 'Develops professional skills'],
                    tips: ['Prioritize important tasks first', 'Take regular breaks', 'Set clear daily goals'],
                    timeRecommendation: '6-8 hours'
                }
            },
            {
                id: 'exercise',
                name: 'Exercise',
                icon: '💪',
                description: 'Physical activity and fitness',
                detailedInfo: {
                    purpose: 'Maintain physical health and fitness through regular exercise and movement.',
                    benefits: ['Improves cardiovascular health', 'Builds strength and endurance', 'Enhances mood and energy'],
                    tips: ['Start with activities you enjoy', 'Begin slowly and increase intensity', 'Stay hydrated'],
                    timeRecommendation: '30-60 minutes'
                }
            },
            {
                id: 'nutrition',
                name: 'Nutrition',
                icon: '🥗',
                description: 'Healthy eating and meal planning',
                detailedInfo: {
                    purpose: 'Nourish your body with healthy, balanced meals and proper nutrition.',
                    benefits: ['Provides sustained energy', 'Supports immune system', 'Improves mental clarity'],
                    tips: ['Plan meals in advance', 'Include variety of fruits and vegetables', 'Stay hydrated'],
                    timeRecommendation: '15-30 minutes per meal'
                }
            },
            {
                id: 'reflection',
                name: 'Reflection',
                icon: '🤔',
                description: 'Daily journaling or self-reflection',
                detailedInfo: {
                    purpose: 'Take time to reflect on your day, thoughts, and personal growth journey.',
                    benefits: ['Increases self-awareness', 'Helps process emotions', 'Tracks personal growth'],
                    tips: ['Write about your day\'s highlights', 'Ask what you learned today', 'Set intentions for tomorrow'],
                    timeRecommendation: '10-20 minutes'
                }
            },
            {
                id: 'connection',
                name: 'Connection',
                icon: '👥',
                description: 'Meaningful social interactions',
                detailedInfo: {
                    purpose: 'Build and maintain meaningful relationships with family, friends, and community.',
                    benefits: ['Reduces feelings of loneliness', 'Provides emotional support', 'Enhances sense of belonging'],
                    tips: ['Reach out to someone you care about', 'Practice active listening', 'Share your thoughts openly'],
                    timeRecommendation: '30-60 minutes'
                }
            }
        ];

        let currentUser = null;
        let completedItems = [];

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            // Check if user is already logged in
            const savedUser = localStorage.getItem('routine_tracker_current_user');
            if (savedUser) {
                currentUser = savedUser;
                loadTracker();
            }

            // Set up event listeners
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
            document.getElementById('logoutBtn').addEventListener('click', handleLogout);
        });

        function handleLogin(e) {
            e.preventDefault();
            const name = document.getElementById('name').value.trim();
            if (!name) return;

            currentUser = name;
            localStorage.setItem('routine_tracker_current_user', name);
            loadTracker();
        }

        function handleLogout() {
            localStorage.removeItem('routine_tracker_current_user');
            currentUser = null;
            completedItems = [];
            document.getElementById('welcomeScreen').classList.remove('hidden');
            document.getElementById('trackerScreen').classList.add('hidden');
            document.getElementById('name').value = '';
        }

        function loadTracker() {
            // Load saved progress for today
            const today = new Date().toISOString().split('T')[0];
            const savedProgress = localStorage.getItem(`routine_progress_${currentUser}_${today}`);
            if (savedProgress) {
                completedItems = JSON.parse(savedProgress);
            } else {
                completedItems = [];
            }

            // Update UI
            document.getElementById('welcomeMessage').textContent = `Welcome back, ${currentUser}! 👋`;
            document.getElementById('currentDate').textContent = new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            });

            renderRoutineItems();
            updateProgress();

            // Show tracker screen
            document.getElementById('welcomeScreen').classList.add('hidden');
            document.getElementById('trackerScreen').classList.remove('hidden');
        }

        function renderRoutineItems() {
            const container = document.getElementById('routineItems');
            container.innerHTML = '';

            routineItems.forEach(item => {
                const isCompleted = completedItems.includes(item.id);
                const itemElement = document.createElement('div');
                itemElement.className = `flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${
                    isCompleted
                        ? 'border-green-200 bg-green-50'
                        : 'border-gray-200 bg-gray-50 hover:border-indigo-200 hover:bg-indigo-50'
                }`;
                
                itemElement.innerHTML = `
                    <div class="flex items-center space-x-4 flex-1">
                        <div class="text-2xl">${item.icon}</div>
                        <div class="flex-1">
                            <h3 class="font-medium ${isCompleted ? 'text-green-800' : 'text-gray-800'}">${item.name}</h3>
                            <p class="text-sm ${isCompleted ? 'text-green-600' : 'text-gray-600'}">${item.description}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="info-btn w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors" title="More info" data-item-id="${item.id}">
                            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </button>
                        <div class="w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                            isCompleted ? 'border-green-500 bg-green-500' : 'border-gray-300'
                        }">
                            ${isCompleted ? '<svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>' : ''}
                        </div>
                    </div>
                `;

                itemElement.addEventListener('click', (e) => {
                    if (!e.target.closest('.info-btn')) {
                        toggleItem(item.id);
                    }
                });

                // Add info button event listener
                const infoBtn = itemElement.querySelector('.info-btn');
                infoBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    showTaskInfo(item);
                });

                container.appendChild(itemElement);
            });
        }

        function toggleItem(itemId) {
            if (completedItems.includes(itemId)) {
                completedItems = completedItems.filter(id => id !== itemId);
            } else {
                completedItems.push(itemId);
            }

            // Save progress
            const today = new Date().toISOString().split('T')[0];
            localStorage.setItem(`routine_progress_${currentUser}_${today}`, JSON.stringify(completedItems));

            // Update UI
            renderRoutineItems();
            updateProgress();
        }

        function updateProgress() {
            const completed = completedItems.length;
            const total = routineItems.length;
            const percentage = Math.round((completed / total) * 100);

            document.getElementById('progressCount').textContent = `${completed}/${total}`;
            document.getElementById('completionRate').textContent = `${percentage}%`;
            document.getElementById('progressBar').style.width = `${percentage}%`;

            // Update motivational message
            let message = '';
            if (percentage === 100) {
                message = "🎉 Amazing! You've completed all your routines today!";
            } else if (percentage >= 75) {
                message = "🔥 You're doing great! Keep it up!";
            } else if (percentage >= 50) {
                message = "💪 Good progress! You're halfway there!";
            } else {
                message = "🌱 Every step counts. You've got this!";
            }
            document.getElementById('motivationalMessage').textContent = message;
        }

        function showTaskInfo(item) {
            const popup = document.createElement('div');
            popup.className = 'fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50';
            popup.innerHTML = `
                <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform">
                    <div class="flex items-center justify-between p-4 border-b border-gray-200">
                        <div class="flex items-center space-x-3">
                            <div class="text-3xl">${item.icon}</div>
                            <h3 class="text-xl font-bold text-gray-900">${item.name}</h3>
                        </div>
                        <button class="close-popup text-gray-400 hover:text-gray-600 transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    <div class="p-6 max-h-[70vh] overflow-y-auto">
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-2">Purpose</h4>
                            <p class="text-gray-600">${item.detailedInfo.purpose}</p>
                        </div>
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-2">Benefits</h4>
                            <ul class="list-disc pl-5 space-y-1">
                                ${item.detailedInfo.benefits.map(benefit => `<li class="text-gray-600">${benefit}</li>`).join('')}
                            </ul>
                        </div>
                        <div class="mb-6">
                            <h4 class="text-lg font-semibold text-gray-800 mb-2">Tips</h4>
                            <ul class="list-disc pl-5 space-y-1">
                                ${item.detailedInfo.tips.map(tip => `<li class="text-gray-600">${tip}</li>`).join('')}
                            </ul>
                        </div>
                        <div class="bg-indigo-50 rounded-lg p-4">
                            <h4 class="text-sm font-semibold text-indigo-800 mb-1">Recommended Time</h4>
                            <p class="text-indigo-700">${item.detailedInfo.timeRecommendation}</p>
                        </div>
                    </div>
                    <div class="border-t border-gray-200 p-4 bg-gray-50 rounded-b-lg">
                        <button class="close-popup w-full bg-indigo-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-indigo-700 transition-colors">
                            Got it
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(popup);

            // Close popup event listeners
            popup.querySelectorAll('.close-popup').forEach(btn => {
                btn.addEventListener('click', () => {
                    document.body.removeChild(popup);
                });
            });

            // Close on outside click
            popup.addEventListener('click', (e) => {
                if (e.target === popup) {
                    document.body.removeChild(popup);
                }
            });
        }
    </script>
</body>
</html>
