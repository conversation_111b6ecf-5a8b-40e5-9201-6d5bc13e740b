'use client';

import { useState } from 'react';
import { User, generateUserId, getCurrentTimestamp } from '@/types';
import { saveUser, getUserByName, setCurrentUser } from '@/utils/storage';

interface WelcomeScreenProps {
  onUserLogin: (userId: string) => void;
}

export function WelcomeScreen({ onUserLogin }: WelcomeScreenProps) {
  const [name, setName] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) return;

    setIsLoading(true);

    try {
      // Check if user already exists
      let user = getUserByName(name.trim());
      
      if (!user) {
        // Create new user
        user = {
          id: generateUserId(name.trim()),
          name: name.trim(),
          createdAt: getCurrentTimestamp(),
          lastActive: getCurrentTimestamp()
        };
        saveUser(user);
      } else {
        // Update last active time
        user.lastActive = getCurrentTimestamp();
        saveUser(user);
      }

      // Set as current user
      setCurrentUser(user.id);
      onUserLogin(user.id);
    } catch (error) {
      console.error('Error logging in:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="text-6xl mb-4">🧱</div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Digital Routine & Results Tracker
          </h1>
          <p className="text-gray-600 text-lg">
            Track your daily growth with intention
          </p>
        </div>

        {/* Description */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            What we're building here 💻✅
          </h2>
          <div className="space-y-3 text-gray-600">
            <div className="flex items-start space-x-2">
              <span className="text-indigo-500 mt-1">•</span>
              <span>Type in your name</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-indigo-500 mt-1">•</span>
              <span>Tick what you've done for the day (Prayer, Study, Hygiene, Work, etc.)</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-indigo-500 mt-1">•</span>
              <span>Submit, and it saves your progress</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-indigo-500 mt-1">•</span>
              <span>Come back anytime before the day ends to update it</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="text-indigo-500 mt-1">•</span>
              <span>System resets at midnight, but keeps a history of your growth</span>
            </div>
          </div>
        </div>

        {/* Login Form */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Ready to track your growth? 📊✨
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                What should we call you?
              </label>
              <div className="relative">
                <input
                  type="text"
                  id="name"
                  value={name}
                  onChange={(e) => {
                    setName(e.target.value);
                    setAccessDenied(false);
                    setErrorMessage('');
                  }}
                  placeholder="Enter your name..."
                  className={`w-full px-4 py-3 pl-12 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-all duration-200 text-gray-900 placeholder-gray-400 bg-white ${
                    accessDenied ? 'border-red-300 bg-red-50' : 'border-gray-300'
                  }`}
                  disabled={isLoading || showSuccess}
                  required
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-xl">
                    {accessDenied ? '🚫' : showSuccess ? '✅' : '😊'}
                  </span>
                </div>
              </div>

              {/* Success Message */}
              {name.trim() && !accessDenied && !errorMessage && (
                <p className="mt-2 text-sm text-indigo-600 animate-fade-in">
                  Hi {name}! Nice to meet you! 👋
                </p>
              )}

              {/* Error Messages */}
              {accessDenied && (
                <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg animate-fade-in">
                  <p className="text-sm text-red-600 font-medium">🚫 Access Denied</p>
                  <p className="text-xs text-red-500 mt-1">{errorMessage}</p>
                  <p className="text-xs text-gray-500 mt-2">
                    💡 Only authorized users can access this tracker. Contact admin for access.
                  </p>
                </div>
              )}

              {errorMessage && !accessDenied && (
                <p className="mt-2 text-sm text-red-600 animate-fade-in">
                  ⚠️ {errorMessage}
                </p>
              )}
            </div>
            <button
              type="submit"
              disabled={isLoading || !name.trim() || showSuccess}
              className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-indigo-700 hover:to-purple-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 disabled:hover:scale-100"
            >
              {showSuccess ? (
                <div className="flex items-center justify-center space-x-2">
                  <span className="text-xl">✅</span>
                  <span>Welcome aboard, {name}!</span>
                </div>
              ) : isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Checking access...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <span>Start My Journey</span>
                  <span className="text-xl">🚀</span>
                </div>
              )}
            </button>
          </form>
        </div>

        {/* Footer */}
        <div className="text-center mt-6 text-gray-500 text-sm">
          <p>No login stress. No judgment. Just you, your goals, and your growth.</p>
        </div>
      </div>
    </div>
  );
}
