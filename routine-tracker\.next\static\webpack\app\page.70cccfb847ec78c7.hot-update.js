"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/config/authorizedUsers.ts":
/*!***************************************!*\
  !*** ./src/config/authorizedUsers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTHORIZED_USERS: () => (/* binding */ AUTHORIZED_USERS),\n/* harmony export */   getDisplayName: () => (/* binding */ getDisplayName),\n/* harmony export */   getTotalAuthorizedUsers: () => (/* binding */ getTotalAuthorizedUsers),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   isUserAuthorized: () => (/* binding */ isUserAuthorized)\n/* harmony export */ });\n// Authorized users configuration\n// Add names of people who should have access to the tracker\n// List of authorized users - add names here to grant access\nconst AUTHORIZED_USERS = [\n    {\n        name: \"Obinna\",\n        displayName: \"Obinna\",\n        role: \"admin\",\n        joinDate: \"2025-01-10\"\n    },\n    {\n        name: \"Daniel\",\n        displayName: \"Daniel\",\n        role: \"user\",\n        joinDate: \"2025-01-10\"\n    }\n];\n// Removed invitation codes - using name-based access only\n// Utility functions\nconst isUserAuthorized = (inputName)=>{\n    const normalizedInput = inputName.toLowerCase().trim();\n    // Check against authorized names only\n    const authorizedUser = AUTHORIZED_USERS.find((user)=>user.name.toLowerCase() === normalizedInput);\n    return authorizedUser || null;\n};\nconst getDisplayName = (user, inputName)=>{\n    return user.displayName || inputName;\n};\n// Admin functions (for future use)\nconst isAdmin = (user)=>{\n    return user.role === 'admin';\n};\nconst getTotalAuthorizedUsers = ()=>{\n    return AUTHORIZED_USERS.length + Object.keys(INVITATION_CODES).length;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/authorizedUsers.ts\n"));

/***/ })

});