"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ROUTINE_ITEMS: () => (/* binding */ DEFAULT_ROUTINE_ITEMS),\n/* harmony export */   calculateCompletionRate: () => (/* binding */ calculateCompletionRate),\n/* harmony export */   generateUserId: () => (/* binding */ generateUserId),\n/* harmony export */   getCurrentDate: () => (/* binding */ getCurrentDate),\n/* harmony export */   getCurrentTimestamp: () => (/* binding */ getCurrentTimestamp),\n/* harmony export */   isNewDay: () => (/* binding */ isNewDay)\n/* harmony export */ });\n// Core data types for the Digital Routine & Results Tracker\n// Default routine items that every user gets\nconst DEFAULT_ROUTINE_ITEMS = [\n    {\n        id: 'prayer',\n        name: 'Prayer',\n        icon: '🙏',\n        description: 'Daily spiritual practice and reflection'\n    },\n    {\n        id: 'study',\n        name: 'Study',\n        icon: '📚',\n        description: 'Learning, reading, or skill development'\n    },\n    {\n        id: 'hygiene',\n        name: 'Hygiene',\n        icon: '🧼',\n        description: 'Personal care and cleanliness'\n    },\n    {\n        id: 'work',\n        name: 'Work',\n        icon: '💼',\n        description: 'Professional tasks and responsibilities'\n    },\n    {\n        id: 'exercise',\n        name: 'Exercise',\n        icon: '💪',\n        description: 'Physical activity and fitness'\n    },\n    {\n        id: 'nutrition',\n        name: 'Nutrition',\n        icon: '🥗',\n        description: 'Healthy eating and meal planning'\n    },\n    {\n        id: 'reflection',\n        name: 'Reflection',\n        icon: '🤔',\n        description: 'Daily journaling or self-reflection'\n    },\n    {\n        id: 'connection',\n        name: 'Connection',\n        icon: '👥',\n        description: 'Meaningful social interactions'\n    }\n];\n// Utility functions for date handling\nconst getCurrentDate = ()=>{\n    return new Date().toISOString().split('T')[0];\n};\nconst getCurrentTimestamp = ()=>{\n    return new Date().toISOString();\n};\n// Check if it's a new day (for reset logic)\nconst isNewDay = (lastDate)=>{\n    return getCurrentDate() !== lastDate;\n};\n// Calculate completion rate\nconst calculateCompletionRate = (completed, total)=>{\n    return Math.round(completed.length / total * 100);\n};\n// Generate user ID (simple approach for now)\nconst generateUserId = (name)=>{\n    const timestamp = Date.now();\n    const nameHash = name.toLowerCase().replace(/\\s+/g, '-');\n    return \"\".concat(nameHash, \"-\").concat(timestamp);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/index.ts\n"));

/***/ })

});