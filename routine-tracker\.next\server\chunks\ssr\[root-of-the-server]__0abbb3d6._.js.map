{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/Rr%201.0/routine-tracker/src/types/index.ts"], "sourcesContent": ["// Core data types for the Digital Routine & Results Tracker\n\nexport interface RoutineItem {\n  id: string;\n  name: string;\n  icon: string;\n  description?: string;\n}\n\nexport interface DailyProgress {\n  userId: string;\n  date: string; // YYYY-MM-DD format\n  completedItems: string[]; // Array of routine item IDs\n  lastUpdated: string; // ISO timestamp\n}\n\nexport interface User {\n  id: string;\n  name: string;\n  createdAt: string; // ISO timestamp\n  lastActive: string; // ISO timestamp\n}\n\nexport interface HistoricalData {\n  userId: string;\n  date: string; // YYYY-MM-DD format\n  completedItems: string[];\n  completionRate: number; // Percentage (0-100)\n  streak: number; // Days in a row with activity\n}\n\n// Default routine items that every user gets\nexport const DEFAULT_ROUTINE_ITEMS: RoutineItem[] = [\n  {\n    id: 'prayer',\n    name: 'Prayer',\n    icon: '🙏',\n    description: 'Daily spiritual practice and reflection'\n  },\n  {\n    id: 'study',\n    name: 'Study',\n    icon: '📚',\n    description: 'Learning, reading, or skill development'\n  },\n  {\n    id: 'hygiene',\n    name: 'Hygiene',\n    icon: '🧼',\n    description: 'Personal care and cleanliness'\n  },\n  {\n    id: 'work',\n    name: 'Work',\n    icon: '💼',\n    description: 'Professional tasks and responsibilities'\n  },\n  {\n    id: 'exercise',\n    name: 'Exercise',\n    icon: '💪',\n    description: 'Physical activity and fitness'\n  },\n  {\n    id: 'nutrition',\n    name: 'Nutrition',\n    icon: '🥗',\n    description: 'Healthy eating and meal planning'\n  },\n  {\n    id: 'reflection',\n    name: 'Reflection',\n    icon: '🤔',\n    description: 'Daily journaling or self-reflection'\n  },\n  {\n    id: 'connection',\n    name: 'Connection',\n    icon: '👥',\n    description: 'Meaningful social interactions'\n  }\n];\n\n// Utility functions for date handling\nexport const getCurrentDate = (): string => {\n  return new Date().toISOString().split('T')[0];\n};\n\nexport const getCurrentTimestamp = (): string => {\n  return new Date().toISOString();\n};\n\n// Check if it's a new day (for reset logic)\nexport const isNewDay = (lastDate: string): boolean => {\n  return getCurrentDate() !== lastDate;\n};\n\n// Calculate completion rate\nexport const calculateCompletionRate = (completed: string[], total: number): number => {\n  return Math.round((completed.length / total) * 100);\n};\n\n// Generate user ID (simple approach for now)\nexport const generateUserId = (name: string): string => {\n  const timestamp = Date.now();\n  const nameHash = name.toLowerCase().replace(/\\s+/g, '-');\n  return `${nameHash}-${timestamp}`;\n};\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;;;;;AAgCrD,MAAM,wBAAuC;IAClD;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;IACf;CACD;AAGM,MAAM,iBAAiB;IAC5B,OAAO,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AAC/C;AAEO,MAAM,sBAAsB;IACjC,OAAO,IAAI,OAAO,WAAW;AAC/B;AAGO,MAAM,WAAW,CAAC;IACvB,OAAO,qBAAqB;AAC9B;AAGO,MAAM,0BAA0B,CAAC,WAAqB;IAC3D,OAAO,KAAK,KAAK,CAAC,AAAC,UAAU,MAAM,GAAG,QAAS;AACjD;AAGO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,WAAW,KAAK,WAAW,GAAG,OAAO,CAAC,QAAQ;IACpD,OAAO,GAAG,SAAS,CAAC,EAAE,WAAW;AACnC", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/Rr%201.0/routine-tracker/src/utils/storage.ts"], "sourcesContent": ["// Local storage utilities for the Digital Routine & Results Tracker\nimport { User, DailyProgress, HistoricalData, getCurrentDate, getCurrentTimestamp } from '@/types';\n\nconst STORAGE_KEYS = {\n  USERS: 'routine_tracker_users',\n  DAILY_PROGRESS: 'routine_tracker_daily_progress',\n  HISTORICAL_DATA: 'routine_tracker_historical_data',\n  CURRENT_USER: 'routine_tracker_current_user'\n};\n\n// User management\nexport const saveUser = (user: User): void => {\n  if (typeof window === 'undefined') return;\n  \n  const users = getUsers();\n  const existingIndex = users.findIndex(u => u.id === user.id);\n  \n  if (existingIndex >= 0) {\n    users[existingIndex] = user;\n  } else {\n    users.push(user);\n  }\n  \n  localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));\n};\n\nexport const getUsers = (): User[] => {\n  if (typeof window === 'undefined') return [];\n  \n  const stored = localStorage.getItem(STORAGE_KEYS.USERS);\n  return stored ? JSON.parse(stored) : [];\n};\n\nexport const getUserByName = (name: string): User | null => {\n  const users = getUsers();\n  return users.find(u => u.name.toLowerCase() === name.toLowerCase()) || null;\n};\n\nexport const setCurrentUser = (userId: string): void => {\n  if (typeof window === 'undefined') return;\n  localStorage.setItem(STORAGE_KEYS.CURRENT_USER, userId);\n};\n\nexport const getCurrentUser = (): string | null => {\n  if (typeof window === 'undefined') return null;\n  return localStorage.getItem(STORAGE_KEYS.CURRENT_USER);\n};\n\n// Daily progress management\nexport const saveDailyProgress = (progress: DailyProgress): void => {\n  if (typeof window === 'undefined') return;\n  \n  const allProgress = getDailyProgress();\n  const key = `${progress.userId}_${progress.date}`;\n  allProgress[key] = progress;\n  \n  localStorage.setItem(STORAGE_KEYS.DAILY_PROGRESS, JSON.stringify(allProgress));\n};\n\nexport const getDailyProgress = (): Record<string, DailyProgress> => {\n  if (typeof window === 'undefined') return {};\n  \n  const stored = localStorage.getItem(STORAGE_KEYS.DAILY_PROGRESS);\n  return stored ? JSON.parse(stored) : {};\n};\n\nexport const getTodayProgress = (userId: string): DailyProgress | null => {\n  const allProgress = getDailyProgress();\n  const key = `${userId}_${getCurrentDate()}`;\n  return allProgress[key] || null;\n};\n\nexport const getUserProgress = (userId: string, date: string): DailyProgress | null => {\n  const allProgress = getDailyProgress();\n  const key = `${userId}_${date}`;\n  return allProgress[key] || null;\n};\n\n// Historical data management\nexport const saveHistoricalData = (data: HistoricalData): void => {\n  if (typeof window === 'undefined') return;\n  \n  const allHistorical = getHistoricalData();\n  const key = `${data.userId}_${data.date}`;\n  allHistorical[key] = data;\n  \n  localStorage.setItem(STORAGE_KEYS.HISTORICAL_DATA, JSON.stringify(allHistorical));\n};\n\nexport const getHistoricalData = (): Record<string, HistoricalData> => {\n  if (typeof window === 'undefined') return {};\n  \n  const stored = localStorage.getItem(STORAGE_KEYS.HISTORICAL_DATA);\n  return stored ? JSON.parse(stored) : {};\n};\n\nexport const getUserHistoricalData = (userId: string, days: number = 30): HistoricalData[] => {\n  const allHistorical = getHistoricalData();\n  const userHistorical: HistoricalData[] = [];\n  \n  // Get last N days of data\n  for (let i = 0; i < days; i++) {\n    const date = new Date();\n    date.setDate(date.getDate() - i);\n    const dateStr = date.toISOString().split('T')[0];\n    const key = `${userId}_${dateStr}`;\n    \n    if (allHistorical[key]) {\n      userHistorical.push(allHistorical[key]);\n    }\n  }\n  \n  return userHistorical.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());\n};\n\n// Daily reset logic\nexport const checkAndPerformDailyReset = (userId: string): boolean => {\n  const todayProgress = getTodayProgress(userId);\n  const currentDate = getCurrentDate();\n  \n  // If no progress for today, check if we need to archive yesterday's data\n  if (!todayProgress) {\n    const yesterday = new Date();\n    yesterday.setDate(yesterday.getDate() - 1);\n    const yesterdayStr = yesterday.toISOString().split('T')[0];\n    \n    const yesterdayProgress = getUserProgress(userId, yesterdayStr);\n    if (yesterdayProgress) {\n      // Archive yesterday's progress to historical data\n      const historicalData: HistoricalData = {\n        userId: yesterdayProgress.userId,\n        date: yesterdayProgress.date,\n        completedItems: yesterdayProgress.completedItems,\n        completionRate: Math.round((yesterdayProgress.completedItems.length / 8) * 100), // 8 default items\n        streak: calculateStreak(userId, yesterdayStr)\n      };\n      \n      saveHistoricalData(historicalData);\n    }\n    \n    return true; // New day, reset needed\n  }\n  \n  return false; // Same day, no reset needed\n};\n\n// Calculate current streak\nexport const calculateStreak = (userId: string, endDate: string): number => {\n  let streak = 0;\n  const date = new Date(endDate);\n  \n  while (true) {\n    const dateStr = date.toISOString().split('T')[0];\n    const progress = getUserProgress(userId, dateStr);\n    \n    if (progress && progress.completedItems.length > 0) {\n      streak++;\n      date.setDate(date.getDate() - 1);\n    } else {\n      break;\n    }\n  }\n  \n  return streak;\n};\n\n// Clear all data (for testing/reset)\nexport const clearAllData = (): void => {\n  if (typeof window === 'undefined') return;\n  \n  Object.values(STORAGE_KEYS).forEach(key => {\n    localStorage.removeItem(key);\n  });\n};\n"], "names": [], "mappings": "AAAA,oEAAoE;;;;;;;;;;;;;;;;;;AACpE;;AAEA,MAAM,eAAe;IACnB,OAAO;IACP,gBAAgB;IAChB,iBAAiB;IACjB,cAAc;AAChB;AAGO,MAAM,WAAW,CAAC;IACvB,wCAAmC;;IAEnC,MAAM;IACN,MAAM;AASR;AAEO,MAAM,WAAW;IACtB,wCAAmC,OAAO,EAAE;;IAE5C,MAAM;AAER;AAEO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,QAAQ;IACd,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,WAAW,OAAO,KAAK,WAAW,OAAO;AACzE;AAEO,MAAM,iBAAiB,CAAC;IAC7B,wCAAmC;;AAErC;AAEO,MAAM,iBAAiB;IAC5B,wCAAmC,OAAO;;AAE5C;AAGO,MAAM,oBAAoB,CAAC;IAChC,wCAAmC;;IAEnC,MAAM;IACN,MAAM;AAIR;AAEO,MAAM,mBAAmB;IAC9B,wCAAmC,OAAO,CAAC;;IAE3C,MAAM;AAER;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,cAAc;IACpB,MAAM,MAAM,GAAG,OAAO,CAAC,EAAE,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,KAAK;IAC3C,OAAO,WAAW,CAAC,IAAI,IAAI;AAC7B;AAEO,MAAM,kBAAkB,CAAC,QAAgB;IAC9C,MAAM,cAAc;IACpB,MAAM,MAAM,GAAG,OAAO,CAAC,EAAE,MAAM;IAC/B,OAAO,WAAW,CAAC,IAAI,IAAI;AAC7B;AAGO,MAAM,qBAAqB,CAAC;IACjC,wCAAmC;;IAEnC,MAAM;IACN,MAAM;AAIR;AAEO,MAAM,oBAAoB;IAC/B,wCAAmC,OAAO,CAAC;;IAE3C,MAAM;AAER;AAEO,MAAM,wBAAwB,CAAC,QAAgB,OAAe,EAAE;IACrE,MAAM,gBAAgB;IACtB,MAAM,iBAAmC,EAAE;IAE3C,0BAA0B;IAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;QAC7B,MAAM,OAAO,IAAI;QACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;QAC9B,MAAM,UAAU,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAChD,MAAM,MAAM,GAAG,OAAO,CAAC,EAAE,SAAS;QAElC,IAAI,aAAa,CAAC,IAAI,EAAE;YACtB,eAAe,IAAI,CAAC,aAAa,CAAC,IAAI;QACxC;IACF;IAEA,OAAO,eAAe,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;AAC5F;AAGO,MAAM,4BAA4B,CAAC;IACxC,MAAM,gBAAgB,iBAAiB;IACvC,MAAM,cAAc,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;IAEjC,yEAAyE;IACzE,IAAI,CAAC,eAAe;QAClB,MAAM,YAAY,IAAI;QACtB,UAAU,OAAO,CAAC,UAAU,OAAO,KAAK;QACxC,MAAM,eAAe,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAE1D,MAAM,oBAAoB,gBAAgB,QAAQ;QAClD,IAAI,mBAAmB;YACrB,kDAAkD;YAClD,MAAM,iBAAiC;gBACrC,QAAQ,kBAAkB,MAAM;gBAChC,MAAM,kBAAkB,IAAI;gBAC5B,gBAAgB,kBAAkB,cAAc;gBAChD,gBAAgB,KAAK,KAAK,CAAC,AAAC,kBAAkB,cAAc,CAAC,MAAM,GAAG,IAAK;gBAC3E,QAAQ,gBAAgB,QAAQ;YAClC;YAEA,mBAAmB;QACrB;QAEA,OAAO,MAAM,wBAAwB;IACvC;IAEA,OAAO,OAAO,4BAA4B;AAC5C;AAGO,MAAM,kBAAkB,CAAC,QAAgB;IAC9C,IAAI,SAAS;IACb,MAAM,OAAO,IAAI,KAAK;IAEtB,MAAO,KAAM;QACX,MAAM,UAAU,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAChD,MAAM,WAAW,gBAAgB,QAAQ;QAEzC,IAAI,YAAY,SAAS,cAAc,CAAC,MAAM,GAAG,GAAG;YAClD;YACA,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;QAChC,OAAO;YACL;QACF;IACF;IAEA,OAAO;AACT;AAGO,MAAM,eAAe;IAC1B,wCAAmC;;AAKrC", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/Rr%201.0/routine-tracker/src/components/RoutineTracker.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { \n  DEFAULT_ROUTINE_ITEMS, \n  DailyProgress, \n  getCurrentDate, \n  getCurrentTimestamp,\n  calculateCompletionRate \n} from '@/types';\nimport { \n  getTodayProgress, \n  saveDailyProgress, \n  checkAndPerformDailyReset,\n  getUserByName,\n  setCurrentUser \n} from '@/utils/storage';\n\ninterface RoutineTrackerProps {\n  userId: string;\n  onLogout: () => void;\n}\n\nexport function RoutineTracker({ userId, onLogout }: RoutineTrackerProps) {\n  const [progress, setProgress] = useState<DailyProgress | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [isSaving, setIsSaving] = useState(false);\n  const [user, setUser] = useState<any>(null);\n\n  useEffect(() => {\n    initializeTracker();\n  }, [userId]);\n\n  const initializeTracker = async () => {\n    setIsLoading(true);\n    \n    try {\n      // Check for daily reset\n      checkAndPerformDailyReset(userId);\n      \n      // Get user info\n      const users = JSON.parse(localStorage.getItem('routine_tracker_users') || '[]');\n      const currentUser = users.find((u: any) => u.id === userId);\n      setUser(currentUser);\n      \n      // Get today's progress\n      const todayProgress = getTodayProgress(userId);\n      \n      if (todayProgress) {\n        setProgress(todayProgress);\n      } else {\n        // Create new progress for today\n        const newProgress: DailyProgress = {\n          userId,\n          date: getCurrentDate(),\n          completedItems: [],\n          lastUpdated: getCurrentTimestamp()\n        };\n        setProgress(newProgress);\n        saveDailyProgress(newProgress);\n      }\n    } catch (error) {\n      console.error('Error initializing tracker:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const toggleItem = async (itemId: string) => {\n    if (!progress || isSaving) return;\n\n    setIsSaving(true);\n    \n    try {\n      const updatedItems = progress.completedItems.includes(itemId)\n        ? progress.completedItems.filter(id => id !== itemId)\n        : [...progress.completedItems, itemId];\n\n      const updatedProgress: DailyProgress = {\n        ...progress,\n        completedItems: updatedItems,\n        lastUpdated: getCurrentTimestamp()\n      };\n\n      setProgress(updatedProgress);\n      saveDailyProgress(updatedProgress);\n    } catch (error) {\n      console.error('Error updating progress:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  const handleLogout = () => {\n    setCurrentUser('');\n    onLogout();\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading your tracker...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!progress) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600\">Error loading your progress. Please try again.</p>\n          <button \n            onClick={initializeTracker}\n            className=\"mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\"\n          >\n            Retry\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  const completionRate = calculateCompletionRate(progress.completedItems, DEFAULT_ROUTINE_ITEMS.length);\n  const currentDate = new Date().toLocaleDateString('en-US', { \n    weekday: 'long', \n    year: 'numeric', \n    month: 'long', \n    day: 'numeric' \n  });\n\n  return (\n    <div className=\"min-h-screen p-4\">\n      <div className=\"max-w-2xl mx-auto\">\n        {/* Header */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mb-6\">\n          <div className=\"flex justify-between items-start mb-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">\n                Welcome back, {user?.name || 'Friend'}! 👋\n              </h1>\n              <p className=\"text-gray-600\">{currentDate}</p>\n            </div>\n            <button\n              onClick={handleLogout}\n              className=\"text-gray-500 hover:text-gray-700 text-sm underline\"\n            >\n              Switch User\n            </button>\n          </div>\n          \n          {/* Progress Summary */}\n          <div className=\"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm text-gray-600\">Today's Progress</p>\n                <p className=\"text-2xl font-bold text-indigo-600\">\n                  {progress.completedItems.length}/{DEFAULT_ROUTINE_ITEMS.length}\n                </p>\n              </div>\n              <div className=\"text-right\">\n                <p className=\"text-sm text-gray-600\">Completion Rate</p>\n                <p className=\"text-2xl font-bold text-indigo-600\">{completionRate}%</p>\n              </div>\n            </div>\n            \n            {/* Progress Bar */}\n            <div className=\"mt-4\">\n              <div className=\"bg-gray-200 rounded-full h-2\">\n                <div \n                  className=\"bg-indigo-600 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${completionRate}%` }}\n                ></div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Routine Items */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-6\">\n            Daily Routine Checklist ✅\n          </h2>\n          \n          <div className=\"space-y-3\">\n            {DEFAULT_ROUTINE_ITEMS.map((item) => {\n              const isCompleted = progress.completedItems.includes(item.id);\n              \n              return (\n                <div\n                  key={item.id}\n                  className={`flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer ${\n                    isCompleted\n                      ? 'border-green-200 bg-green-50'\n                      : 'border-gray-200 bg-gray-50 hover:border-indigo-200 hover:bg-indigo-50'\n                  }`}\n                  onClick={() => toggleItem(item.id)}\n                >\n                  <div className=\"flex items-center space-x-4 flex-1\">\n                    <div className=\"text-2xl\">{item.icon}</div>\n                    <div className=\"flex-1\">\n                      <h3 className={`font-medium ${isCompleted ? 'text-green-800' : 'text-gray-800'}`}>\n                        {item.name}\n                      </h3>\n                      <p className={`text-sm ${isCompleted ? 'text-green-600' : 'text-gray-600'}`}>\n                        {item.description}\n                      </p>\n                    </div>\n                  </div>\n                  \n                  <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${\n                    isCompleted\n                      ? 'border-green-500 bg-green-500'\n                      : 'border-gray-300'\n                  }`}>\n                    {isCompleted && (\n                      <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    )}\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n          \n          {/* Save Status */}\n          {isSaving && (\n            <div className=\"mt-4 text-center text-sm text-gray-600\">\n              <div className=\"inline-flex items-center space-x-2\">\n                <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600\"></div>\n                <span>Saving...</span>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Motivational Footer */}\n        <div className=\"text-center mt-6 text-gray-600\">\n          <p className=\"text-sm\">\n            {completionRate === 100 \n              ? \"🎉 Amazing! You've completed all your routines today!\" \n              : completionRate >= 75 \n              ? \"🔥 You're doing great! Keep it up!\" \n              : completionRate >= 50 \n              ? \"💪 Good progress! You're halfway there!\" \n              : \"🌱 Every step counts. You've got this!\"\n            }\n          </p>\n          <p className=\"text-xs mt-2\">\n            Progress auto-saves • Resets at midnight • Your growth is tracked\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AAVA;;;;;AAuBO,SAAS,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAuB;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAEtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB;QACxB,aAAa;QAEb,IAAI;YACF,wBAAwB;YACxB,CAAA,GAAA,uHAAA,CAAA,4BAAyB,AAAD,EAAE;YAE1B,gBAAgB;YAChB,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,4BAA4B;YAC1E,MAAM,cAAc,MAAM,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,KAAK;YACpD,QAAQ;YAER,uBAAuB;YACvB,MAAM,gBAAgB,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD,EAAE;YAEvC,IAAI,eAAe;gBACjB,YAAY;YACd,OAAO;gBACL,gCAAgC;gBAChC,MAAM,cAA6B;oBACjC;oBACA,MAAM,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD;oBACnB,gBAAgB,EAAE;oBAClB,aAAa,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD;gBACjC;gBACA,YAAY;gBACZ,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,YAAY,UAAU;QAE3B,YAAY;QAEZ,IAAI;YACF,MAAM,eAAe,SAAS,cAAc,CAAC,QAAQ,CAAC,UAClD,SAAS,cAAc,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO,UAC5C;mBAAI,SAAS,cAAc;gBAAE;aAAO;YAExC,MAAM,kBAAiC;gBACrC,GAAG,QAAQ;gBACX,gBAAgB;gBAChB,aAAa,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD;YACjC;YAEA,YAAY;YACZ,CAAA,GAAA,uHAAA,CAAA,oBAAiB,AAAD,EAAE;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,eAAe;QACnB,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE;QACf;IACF;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAe;;;;;;kCAC5B,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,MAAM,iBAAiB,CAAA,GAAA,qHAAA,CAAA,0BAAuB,AAAD,EAAE,SAAS,cAAc,EAAE,qHAAA,CAAA,wBAAqB,CAAC,MAAM;IACpG,MAAM,cAAc,IAAI,OAAO,kBAAkB,CAAC,SAAS;QACzD,SAAS;QACT,MAAM;QACN,OAAO;QACP,KAAK;IACP;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;;gDAAmC;gDAChC,MAAM,QAAQ;gDAAS;;;;;;;sDAExC,8OAAC;4CAAE,WAAU;sDAAiB;;;;;;;;;;;;8CAEhC,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;;wDACV,SAAS,cAAc,CAAC,MAAM;wDAAC;wDAAE,qHAAA,CAAA,wBAAqB,CAAC,MAAM;;;;;;;;;;;;;sDAGlE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,8OAAC;oDAAE,WAAU;;wDAAsC;wDAAe;;;;;;;;;;;;;;;;;;;8CAKtE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,eAAe,CAAC,CAAC;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQ/C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAIzD,8OAAC;4BAAI,WAAU;sCACZ,qHAAA,CAAA,wBAAqB,CAAC,GAAG,CAAC,CAAC;gCAC1B,MAAM,cAAc,SAAS,cAAc,CAAC,QAAQ,CAAC,KAAK,EAAE;gCAE5D,qBACE,8OAAC;oCAEC,WAAW,CAAC,wEAAwE,EAClF,cACI,iCACA,yEACJ;oCACF,SAAS,IAAM,WAAW,KAAK,EAAE;;sDAEjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAY,KAAK,IAAI;;;;;;8DACpC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAW,CAAC,YAAY,EAAE,cAAc,mBAAmB,iBAAiB;sEAC7E,KAAK,IAAI;;;;;;sEAEZ,8OAAC;4DAAE,WAAW,CAAC,QAAQ,EAAE,cAAc,mBAAmB,iBAAiB;sEACxE,KAAK,WAAW;;;;;;;;;;;;;;;;;;sDAKvB,8OAAC;4CAAI,WAAW,CAAC,+DAA+D,EAC9E,cACI,kCACA,mBACJ;sDACC,6BACC,8OAAC;gDAAI,WAAU;gDAAqB,MAAK;gDAAe,SAAQ;0DAC9D,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;;;;;;;mCA3B1J,KAAK,EAAE;;;;;4BAiClB;;;;;;wBAID,0BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;8BAOd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCACV,mBAAmB,MAChB,0DACA,kBAAkB,KAClB,uCACA,kBAAkB,KAClB,4CACA;;;;;;sCAGN,8OAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/Rr%201.0/routine-tracker/src/components/WelcomeScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { User, generateUserId, getCurrentTimestamp } from '@/types';\nimport { saveUser, getUserByName, setCurrentUser } from '@/utils/storage';\n\ninterface WelcomeScreenProps {\n  onUserLogin: (userId: string) => void;\n}\n\nexport function WelcomeScreen({ onUserLogin }: WelcomeScreenProps) {\n  const [name, setName] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!name.trim()) return;\n\n    setIsLoading(true);\n\n    try {\n      // Check if user already exists\n      let user = getUserByName(name.trim());\n      \n      if (!user) {\n        // Create new user\n        user = {\n          id: generateUserId(name.trim()),\n          name: name.trim(),\n          createdAt: getCurrentTimestamp(),\n          lastActive: getCurrentTimestamp()\n        };\n        saveUser(user);\n      } else {\n        // Update last active time\n        user.lastActive = getCurrentTimestamp();\n        saveUser(user);\n      }\n\n      // Set as current user\n      setCurrentUser(user.id);\n      onUserLogin(user.id);\n    } catch (error) {\n      console.error('Error logging in:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center p-4\">\n      <div className=\"max-w-md w-full\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <div className=\"text-6xl mb-4\">🧱</div>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Digital Routine & Results Tracker\n          </h1>\n          <p className=\"text-gray-600 text-lg\">\n            Track your daily growth with intention\n          </p>\n        </div>\n\n        {/* Description */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mb-6\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">\n            What we're building here 💻✅\n          </h2>\n          <div className=\"space-y-3 text-gray-600\">\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-indigo-500 mt-1\">•</span>\n              <span>Type in your name</span>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-indigo-500 mt-1\">•</span>\n              <span>Tick what you've done for the day (Prayer, Study, Hygiene, Work, etc.)</span>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-indigo-500 mt-1\">•</span>\n              <span>Submit, and it saves your progress</span>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-indigo-500 mt-1\">•</span>\n              <span>Come back anytime before the day ends to update it</span>\n            </div>\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-indigo-500 mt-1\">•</span>\n              <span>System resets at midnight, but keeps a history of your growth</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Login Form */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">\n            Ready to track your growth? 📊✨\n          </h3>\n          <form onSubmit={handleSubmit} className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Your Name\n              </label>\n              <input\n                type=\"text\"\n                id=\"name\"\n                value={name}\n                onChange={(e) => setName(e.target.value)}\n                placeholder=\"Enter your name...\"\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors\"\n                disabled={isLoading}\n                required\n              />\n            </div>\n            <button\n              type=\"submit\"\n              disabled={isLoading || !name.trim()}\n              className=\"w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center space-x-2\">\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"></div>\n                  <span>Getting started...</span>\n                </div>\n              ) : (\n                'Start Tracking 🔥'\n              )}\n            </button>\n          </form>\n        </div>\n\n        {/* Footer */}\n        <div className=\"text-center mt-6 text-gray-500 text-sm\">\n          <p>No login stress. No judgment. Just you, your goals, and your growth.</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUO,SAAS,cAAc,EAAE,WAAW,EAAsB;IAC/D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,KAAK,IAAI,IAAI;QAElB,aAAa;QAEb,IAAI;YACF,+BAA+B;YAC/B,IAAI,OAAO,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,IAAI;YAElC,IAAI,CAAC,MAAM;gBACT,kBAAkB;gBAClB,OAAO;oBACL,IAAI,CAAA,GAAA,qHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI;oBAC5B,MAAM,KAAK,IAAI;oBACf,WAAW,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD;oBAC7B,YAAY,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD;gBAChC;gBACA,CAAA,GAAA,uHAAA,CAAA,WAAQ,AAAD,EAAE;YACX,OAAO;gBACL,0BAA0B;gBAC1B,KAAK,UAAU,GAAG,CAAA,GAAA,qHAAA,CAAA,sBAAmB,AAAD;gBACpC,CAAA,GAAA,uHAAA,CAAA,WAAQ,AAAD,EAAE;YACX;YAEA,sBAAsB;YACtB,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,EAAE;YACtB,YAAY,KAAK,EAAE;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAuB;;;;;;sDACvC,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAuB;;;;;;sDACvC,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAuB;;;;;;sDACvC,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAuB;;;;;;sDACvC,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAuB;;;;;;sDACvC,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAMZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAGzD,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA+C;;;;;;sDAG/E,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACvC,aAAY;4CACZ,WAAU;4CACV,UAAU;4CACV,QAAQ;;;;;;;;;;;;8CAGZ,8OAAC;oCACC,MAAK;oCACL,UAAU,aAAa,CAAC,KAAK,IAAI;oCACjC,WAAU;8CAET,0BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;+CAGR;;;;;;;;;;;;;;;;;;8BAOR,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCAAE;;;;;;;;;;;;;;;;;;;;;;AAKb", "debugId": null}}, {"offset": {"line": 1080, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/Rr%201.0/routine-tracker/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { RoutineTracker } from '@/components/RoutineTracker';\nimport { WelcomeScreen } from '@/components/WelcomeScreen';\nimport { getCurrentUser } from '@/utils/storage';\n\nexport default function Home() {\n  const [currentUserId, setCurrentUserId] = useState<string | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n\n  useEffect(() => {\n    // Check if user is already logged in\n    const userId = getCurrentUser();\n    setCurrentUserId(userId);\n    setIsLoading(false);\n  }, []);\n\n  const handleUserLogin = (userId: string) => {\n    setCurrentUserId(userId);\n  };\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading your routine tracker...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      {currentUserId ? (\n        <RoutineTracker userId={currentUserId} onLogout={() => setCurrentUserId(null)} />\n      ) : (\n        <WelcomeScreen onUserLogin={handleUserLogin} />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qCAAqC;QACrC,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD;QAC5B,iBAAiB;QACjB,aAAa;IACf,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;IACnB;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACZ,8BACC,8OAAC,oIAAA,CAAA,iBAAc;YAAC,QAAQ;YAAe,UAAU,IAAM,iBAAiB;;;;;iCAExE,8OAAC,mIAAA,CAAA,gBAAa;YAAC,aAAa;;;;;;;;;;;AAIpC", "debugId": null}}, {"offset": {"line": 1166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/Rr%201.0/routine-tracker/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/Rr%201.0/routine-tracker/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/archive-2025-06-05T142720%2B0200/Practice/Rr%201.0/routine-tracker/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}