"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/WelcomeScreen.tsx":
/*!******************************************!*\
  !*** ./src/components/WelcomeScreen.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WelcomeScreen: () => (/* binding */ WelcomeScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ WelcomeScreen auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction WelcomeScreen(param) {\n    let { onUserLogin } = param;\n    _s();\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!name.trim()) return;\n        setIsLoading(true);\n        setAccessDenied(false);\n        setErrorMessage('');\n        try {\n            // Check if user is authorized\n            const authorizedUser = isUserAuthorized(name.trim());\n            if (!authorizedUser) {\n                setAccessDenied(true);\n                setErrorMessage('Your name is not on the authorized list.');\n                setIsLoading(false);\n                return;\n            }\n            // Get display name for authorized user\n            const displayName = getDisplayName(authorizedUser, name.trim());\n            // Check if user already exists in storage\n            let user = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getUserByName)(displayName);\n            if (!user) {\n                // Create new user with display name\n                user = {\n                    id: (0,_types__WEBPACK_IMPORTED_MODULE_2__.generateUserId)(displayName),\n                    name: displayName,\n                    createdAt: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)(),\n                    lastActive: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n                };\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveUser)(user);\n            } else {\n                // Update last active time\n                user.lastActive = (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)();\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveUser)(user);\n            }\n            // Show success message\n            setShowSuccess(true);\n            // Set as current user after a brief delay for better UX\n            setTimeout(()=>{\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(user.id);\n                onUserLogin(user.id);\n            }, 1000);\n        } catch (error) {\n            console.error('Error logging in:', error);\n            setErrorMessage('Something went wrong. Please try again.');\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83E\\uDDF1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Digital Routine & Results Tracker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-lg\",\n                            children: \"Track your daily growth with intention\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: \"What we're building here \\uD83D\\uDCBB✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Type in your name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Tick what you've done for the day (Prayer, Study, Hygiene, Work, etc.)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Submit, and it saves your progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Come back anytime before the day ends to update it\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"System resets at midnight, but keeps a history of your growth\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-800 mb-4\",\n                            children: \"Ready to track your growth? \\uD83D\\uDCCA✨\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"name\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"What should we call you?\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"name\",\n                                                    value: name,\n                                                    onChange: (e)=>{\n                                                        setName(e.target.value);\n                                                        setAccessDenied(false);\n                                                        setErrorMessage('');\n                                                    },\n                                                    placeholder: \"Enter your name...\",\n                                                    className: \"w-full px-4 py-3 pl-12 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-all duration-200 text-gray-900 placeholder-gray-400 bg-white \".concat(accessDenied ? 'border-red-300 bg-red-50' : 'border-gray-300'),\n                                                    disabled: isLoading || showSuccess,\n                                                    required: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl\",\n                                                        children: accessDenied ? '🚫' : showSuccess ? '✅' : '😊'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                        lineNumber: 141,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this),\n                                        name.trim() && !accessDenied && !errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-indigo-600 animate-fade-in\",\n                                            children: [\n                                                \"Hi \",\n                                                name,\n                                                \"! Nice to meet you! \\uD83D\\uDC4B\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 17\n                                        }, this),\n                                        accessDenied && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-2 p-3 bg-red-50 border border-red-200 rounded-lg animate-fade-in\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-red-600 font-medium\",\n                                                    children: \"\\uD83D\\uDEAB Access Denied\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-red-500 mt-1\",\n                                                    children: errorMessage\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500 mt-2\",\n                                                    children: \"\\uD83D\\uDCA1 Only authorized users can access this tracker. Contact admin for access.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this),\n                                        errorMessage && !accessDenied && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"mt-2 text-sm text-red-600 animate-fade-in\",\n                                            children: [\n                                                \"⚠️ \",\n                                                errorMessage\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || !name.trim() || showSuccess,\n                                    className: \"w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-indigo-700 hover:to-purple-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 transform hover:scale-105 disabled:hover:scale-100\",\n                                    children: showSuccess ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: \"✅\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Welcome aboard, \",\n                                                    name,\n                                                    \"!\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 17\n                                    }, this) : isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Checking access...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Start My Journey\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xl\",\n                                                children: \"\\uD83D\\uDE80\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6 text-gray-500 text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No login stress. No judgment. Just you, your goals, and your growth.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(WelcomeScreen, \"vDkXL38GXdR3JHRXxGnsXBYUKeM=\");\n_c = WelcomeScreen;\nvar _c;\n$RefreshReg$(_c, \"WelcomeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WelcomeScreen.tsx\n"));

/***/ })

});