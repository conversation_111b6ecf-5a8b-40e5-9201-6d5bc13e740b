"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/config/authorizedUsers.ts":
/*!***************************************!*\
  !*** ./src/config/authorizedUsers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTHORIZED_USERS: () => (/* binding */ AUTHORIZED_USERS),\n/* harmony export */   getDisplayName: () => (/* binding */ getDisplayName),\n/* harmony export */   getTotalAuthorizedUsers: () => (/* binding */ getTotalAuthorizedUsers),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   isUserAuthorized: () => (/* binding */ isUserAuthorized)\n/* harmony export */ });\n// Authorized users configuration\n// Add names of people who should have access to the tracker\n// List of authorized users - add names here to grant access\nconst AUTHORIZED_USERS = [\n    {\n        name: \"Obinna\",\n        displayName: \"Obinna\",\n        role: \"admin\",\n        joinDate: \"2025-01-10\"\n    },\n    {\n        name: \"Daniel\",\n        displayName: \"Daniel\",\n        role: \"user\",\n        joinDate: \"2025-01-10\"\n    }\n];\n// Removed invitation codes - using name-based access only\n// Utility functions\nconst isUserAuthorized = (inputName)=>{\n    const normalizedInput = inputName.toLowerCase().trim();\n    // Check against authorized names\n    const authorizedUser = AUTHORIZED_USERS.find((user)=>user.name.toLowerCase() === normalizedInput);\n    if (authorizedUser) {\n        return authorizedUser;\n    }\n    // Check against invitation codes\n    const inviteCode = INVITATION_CODES[inputName.toUpperCase().trim()];\n    if (inviteCode) {\n        return inviteCode;\n    }\n    return null;\n};\nconst getDisplayName = (user, inputName)=>{\n    return user.displayName || inputName;\n};\n// Admin functions (for future use)\nconst isAdmin = (user)=>{\n    return user.role === 'admin';\n};\nconst getTotalAuthorizedUsers = ()=>{\n    return AUTHORIZED_USERS.length + Object.keys(INVITATION_CODES).length;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/authorizedUsers.ts\n"));

/***/ })

});