/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDVVNFUiU1QyU1Q0Rlc2t0b3AlNUMlNUNhcmNoaXZlLTIwMjUtMDYtMDVUMTQyNzIwJTJCMDIwMCU1QyU1Q1ByYWN0aWNlJTVDJTVDUnIlMjAxLjAlNUMlNUNyb3V0aW5lLXRyYWNrZXIlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLDhKQUFvSiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcVVNFUlxcXFxEZXNrdG9wXFxcXGFyY2hpdmUtMjAyNS0wNi0wNVQxNDI3MjArMDIwMFxcXFxQcmFjdGljZVxcXFxSciAxLjBcXFxccm91dGluZS10cmFja2VyXFxcXHNyY1xcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGVza3RvcFxcYXJjaGl2ZS0yMDI1LTA2LTA1VDE0MjcyMCswMjAwXFxQcmFjdGljZVxcUnIgMS4wXFxyb3V0aW5lLXRyYWNrZXJcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_RoutineTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/RoutineTracker */ \"(app-pages-browser)/./src/components/RoutineTracker.tsx\");\n/* harmony import */ var _components_WelcomeScreen__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/WelcomeScreen */ \"(app-pages-browser)/./src/components/WelcomeScreen.tsx\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const [currentUserId, setCurrentUserId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            // Check if user is already logged in\n            const userId = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_4__.getCurrentUser)();\n            setCurrentUserId(userId);\n            setIsLoading(false);\n        }\n    }[\"Home.useEffect\"], []);\n    const handleUserLogin = (userId)=>{\n        setCurrentUserId(userId);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading-spinner h-16 w-16 mx-auto mb-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-2xl animate-pulse\",\n                                    children: \"\\uD83E\\uDDF1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold text-gray-800 mb-2 animate-fade-in\",\n                        children: \"Setting up your tracker...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 animate-fade-in-delay\",\n                        children: \"Get ready to track your daily growth! ✨\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 26,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 transition-all duration-500\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-fade-in\",\n            children: currentUserId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoutineTracker__WEBPACK_IMPORTED_MODULE_2__.RoutineTracker, {\n                userId: currentUserId,\n                onLogout: ()=>setCurrentUserId(null)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 48,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_WelcomeScreen__WEBPACK_IMPORTED_MODULE_3__.WelcomeScreen, {\n                onUserLogin: handleUserLogin\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 50,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"yGhjRQA3UpUZgGvpuU5D7O9Wwn0=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/RoutineTracker.tsx":
/*!*******************************************!*\
  !*** ./src/components/RoutineTracker.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RoutineTracker: () => (/* binding */ RoutineTracker)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ RoutineTracker auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction RoutineTracker(param) {\n    let { userId, onLogout } = param;\n    _s();\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutineTracker.useEffect\": ()=>{\n            initializeTracker();\n        }\n    }[\"RoutineTracker.useEffect\"], [\n        userId\n    ]);\n    const initializeTracker = async ()=>{\n        setIsLoading(true);\n        try {\n            // Check for daily reset\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.checkAndPerformDailyReset)(userId);\n            // Get user info\n            const users = JSON.parse(localStorage.getItem('routine_tracker_users') || '[]');\n            const currentUser = users.find((u)=>u.id === userId);\n            setUser(currentUser);\n            // Get today's progress\n            const todayProgress = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getTodayProgress)(userId);\n            if (todayProgress) {\n                setProgress(todayProgress);\n            } else {\n                // Create new progress for today\n                const newProgress = {\n                    userId,\n                    date: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentDate)(),\n                    completedItems: [],\n                    lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n                };\n                setProgress(newProgress);\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(newProgress);\n            }\n        } catch (error) {\n            console.error('Error initializing tracker:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const toggleItem = async (itemId)=>{\n        if (!progress || isSaving) return;\n        setIsSaving(true);\n        try {\n            const updatedItems = progress.completedItems.includes(itemId) ? progress.completedItems.filter((id)=>id !== itemId) : [\n                ...progress.completedItems,\n                itemId\n            ];\n            const updatedProgress = {\n                ...progress,\n                completedItems: updatedItems,\n                lastUpdated: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n            };\n            setProgress(updatedProgress);\n            (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveDailyProgress)(updatedProgress);\n        } catch (error) {\n            console.error('Error updating progress:', error);\n        } finally{\n            setIsSaving(false);\n        }\n    };\n    const handleLogout = ()=>{\n        (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)('');\n        onLogout();\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Loading your tracker...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 101,\n            columnNumber: 7\n        }, this);\n    }\n    if (!progress) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-red-600\",\n                        children: \"Error loading your progress. Please try again.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeTracker,\n                        className: \"mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, this);\n    }\n    const completionRate = (0,_types__WEBPACK_IMPORTED_MODULE_2__.calculateCompletionRate)(progress.completedItems, _types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_ROUTINE_ITEMS.length);\n    const currentDate = new Date().toLocaleDateString('en-US', {\n        weekday: 'long',\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-2xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-start mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: [\n                                                \"Welcome back, \",\n                                                (user === null || user === void 0 ? void 0 : user.name) || 'Friend',\n                                                \"! \\uD83D\\uDC4B\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: currentDate\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogout,\n                                    className: \"text-gray-500 hover:text-gray-700 text-sm underline\",\n                                    children: \"Switch User\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Today's Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        progress.completedItems.length,\n                                                        \"/\",\n                                                        _types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_ROUTINE_ITEMS.length\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-right\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"Completion Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-indigo-600\",\n                                                    children: [\n                                                        completionRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-200 rounded-full h-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-indigo-600 h-2 rounded-full transition-all duration-300\",\n                                            style: {\n                                                width: \"\".concat(completionRate, \"%\")\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-6\",\n                            children: \"Daily Routine Checklist ✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: _types__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_ROUTINE_ITEMS.map((item)=>{\n                                const isCompleted = progress.completedItems.includes(item.id);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center p-4 rounded-lg border-2 transition-all cursor-pointer \".concat(isCompleted ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50 hover:border-indigo-200 hover:bg-indigo-50'),\n                                    onClick: ()=>toggleItem(item.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl\",\n                                                    children: item.icon\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 202,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"font-medium \".concat(isCompleted ? 'text-green-800' : 'text-gray-800'),\n                                                            children: item.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm \".concat(isCompleted ? 'text-green-600' : 'text-gray-600'),\n                                                            children: item.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                            lineNumber: 207,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-6 rounded-full border-2 flex items-center justify-center \".concat(isCompleted ? 'border-green-500 bg-green-500' : 'border-gray-300'),\n                                            children: isCompleted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-white\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this),\n                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center text-sm text-gray-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Saving...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6 text-gray-600\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            children: completionRate === 100 ? \"🎉 Amazing! You've completed all your routines today!\" : completionRate >= 75 ? \"🔥 You're doing great! Keep it up!\" : completionRate >= 50 ? \"💪 Good progress! You're halfway there!\" : \"🌱 Every step counts. You've got this!\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs mt-2 mb-4\",\n                            children: \"Progress auto-saves • Resets at midnight • Your growth is tracked\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4 mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center space-x-2 text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-400\",\n                                            children: \"Crafted with ❤️ by\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-indigo-600 font-semibold\",\n                                                    children: \"Tech Talk\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-indigo-500\",\n                                                    children: \"\\uD83D\\uDCAC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-400 text-xs mt-1\",\n                                    children: \"Empowering your digital growth journey\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                            lineNumber: 257,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n            lineNumber: 136,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\RoutineTracker.tsx\",\n        lineNumber: 135,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutineTracker, \"FfZh3nLRNvXvHExVYScUMjVQAtk=\");\n_c = RoutineTracker;\nvar _c;\n$RefreshReg$(_c, \"RoutineTracker\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/RoutineTracker.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/WelcomeScreen.tsx":
/*!******************************************!*\
  !*** ./src/components/WelcomeScreen.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WelcomeScreen: () => (/* binding */ WelcomeScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* __next_internal_client_entry_do_not_use__ WelcomeScreen auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction WelcomeScreen(param) {\n    let { onUserLogin } = param;\n    _s();\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!name.trim()) return;\n        setIsLoading(true);\n        setAccessDenied(false);\n        setErrorMessage('');\n        try {\n            // Check if user is authorized\n            const authorizedUser = isUserAuthorized(name.trim());\n            if (!authorizedUser) {\n                setAccessDenied(true);\n                setErrorMessage('Your name is not on the authorized list.');\n                setIsLoading(false);\n                return;\n            }\n            // Get display name for authorized user\n            const displayName = getDisplayName(authorizedUser, name.trim());\n            // Check if user already exists in storage\n            let user = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getUserByName)(displayName);\n            if (!user) {\n                // Create new user with display name\n                user = {\n                    id: (0,_types__WEBPACK_IMPORTED_MODULE_2__.generateUserId)(displayName),\n                    name: displayName,\n                    createdAt: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)(),\n                    lastActive: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n                };\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveUser)(user);\n            } else {\n                // Update last active time\n                user.lastActive = (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)();\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveUser)(user);\n            }\n            // Show success message\n            setShowSuccess(true);\n            // Set as current user after a brief delay for better UX\n            setTimeout(()=>{\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(user.id);\n                onUserLogin(user.id);\n            }, 1000);\n        } catch (error) {\n            console.error('Error logging in:', error);\n            setErrorMessage('Something went wrong. Please try again.');\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83E\\uDDF1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Digital Routine & Results Tracker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-lg\",\n                            children: \"Track your daily growth with intention\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: \"What we're building here \\uD83D\\uDCBB✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Type in your name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Tick what you've done for the day (Prayer, Study, Hygiene, Work, etc.)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Submit, and it saves your progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 100,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Come back anytime before the day ends to update it\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"System resets at midnight, but keeps a history of your growth\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-800 mb-4\",\n                            children: \"Ready to track your growth? \\uD83D\\uDCCA✨\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"name\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Your Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"name\",\n                                            value: name,\n                                            onChange: (e)=>setName(e.target.value),\n                                            placeholder: \"Enter your name...\",\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors\",\n                                            disabled: isLoading,\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || !name.trim(),\n                                    className: \"w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Getting started...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this) : 'Start Tracking 🔥'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6 text-gray-500 text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No login stress. No judgment. Just you, your goals, and your growth.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n            lineNumber: 72,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_s(WelcomeScreen, \"vDkXL38GXdR3JHRXxGnsXBYUKeM=\");\n_c = WelcomeScreen;\nvar _c;\n$RefreshReg$(_c, \"WelcomeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WelcomeScreen.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ROUTINE_ITEMS: () => (/* binding */ DEFAULT_ROUTINE_ITEMS),\n/* harmony export */   calculateCompletionRate: () => (/* binding */ calculateCompletionRate),\n/* harmony export */   generateUserId: () => (/* binding */ generateUserId),\n/* harmony export */   getCurrentDate: () => (/* binding */ getCurrentDate),\n/* harmony export */   getCurrentTimestamp: () => (/* binding */ getCurrentTimestamp),\n/* harmony export */   isNewDay: () => (/* binding */ isNewDay)\n/* harmony export */ });\n// Core data types for the Digital Routine & Results Tracker\n// Default routine items that every user gets\nconst DEFAULT_ROUTINE_ITEMS = [\n    {\n        id: 'prayer',\n        name: 'Prayer',\n        icon: '🙏',\n        description: 'Daily spiritual practice and reflection'\n    },\n    {\n        id: 'study',\n        name: 'Study',\n        icon: '📚',\n        description: 'Learning, reading, or skill development'\n    },\n    {\n        id: 'hygiene',\n        name: 'Hygiene',\n        icon: '🧼',\n        description: 'Personal care and cleanliness'\n    },\n    {\n        id: 'work',\n        name: 'Work',\n        icon: '💼',\n        description: 'Professional tasks and responsibilities'\n    },\n    {\n        id: 'exercise',\n        name: 'Exercise',\n        icon: '💪',\n        description: 'Physical activity and fitness'\n    },\n    {\n        id: 'nutrition',\n        name: 'Nutrition',\n        icon: '🥗',\n        description: 'Healthy eating and meal planning'\n    },\n    {\n        id: 'reflection',\n        name: 'Reflection',\n        icon: '🤔',\n        description: 'Daily journaling or self-reflection'\n    },\n    {\n        id: 'connection',\n        name: 'Connection',\n        icon: '👥',\n        description: 'Meaningful social interactions'\n    }\n];\n// Utility functions for date handling\nconst getCurrentDate = ()=>{\n    return new Date().toISOString().split('T')[0];\n};\nconst getCurrentTimestamp = ()=>{\n    return new Date().toISOString();\n};\n// Check if it's a new day (for reset logic)\nconst isNewDay = (lastDate)=>{\n    return getCurrentDate() !== lastDate;\n};\n// Calculate completion rate\nconst calculateCompletionRate = (completed, total)=>{\n    return Math.round(completed.length / total * 100);\n};\n// Generate user ID (simple approach for now)\nconst generateUserId = (name)=>{\n    const timestamp = Date.now();\n    const nameHash = name.toLowerCase().replace(/\\s+/g, '-');\n    return \"\".concat(nameHash, \"-\").concat(timestamp);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy90eXBlcy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQSw0REFBNEQ7QUErQjVELDZDQUE2QztBQUN0QyxNQUFNQSx3QkFBdUM7SUFDbEQ7UUFDRUMsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUgsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUgsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUgsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUgsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUgsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUgsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtJQUNmO0lBQ0E7UUFDRUgsSUFBSTtRQUNKQyxNQUFNO1FBQ05DLE1BQU07UUFDTkMsYUFBYTtJQUNmO0NBQ0QsQ0FBQztBQUVGLHNDQUFzQztBQUMvQixNQUFNQyxpQkFBaUI7SUFDNUIsT0FBTyxJQUFJQyxPQUFPQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtBQUMvQyxFQUFFO0FBRUssTUFBTUMsc0JBQXNCO0lBQ2pDLE9BQU8sSUFBSUgsT0FBT0MsV0FBVztBQUMvQixFQUFFO0FBRUYsNENBQTRDO0FBQ3JDLE1BQU1HLFdBQVcsQ0FBQ0M7SUFDdkIsT0FBT04scUJBQXFCTTtBQUM5QixFQUFFO0FBRUYsNEJBQTRCO0FBQ3JCLE1BQU1DLDBCQUEwQixDQUFDQyxXQUFxQkM7SUFDM0QsT0FBT0MsS0FBS0MsS0FBSyxDQUFDLFVBQVdDLE1BQU0sR0FBR0gsUUFBUztBQUNqRCxFQUFFO0FBRUYsNkNBQTZDO0FBQ3RDLE1BQU1JLGlCQUFpQixDQUFDaEI7SUFDN0IsTUFBTWlCLFlBQVliLEtBQUtjLEdBQUc7SUFDMUIsTUFBTUMsV0FBV25CLEtBQUtvQixXQUFXLEdBQUdDLE9BQU8sQ0FBQyxRQUFRO0lBQ3BELE9BQU8sR0FBZUosT0FBWkUsVUFBUyxLQUFhLE9BQVZGO0FBQ3hCLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcVVNFUlxcRGVza3RvcFxcYXJjaGl2ZS0yMDI1LTA2LTA1VDE0MjcyMCswMjAwXFxQcmFjdGljZVxcUnIgMS4wXFxyb3V0aW5lLXRyYWNrZXJcXHNyY1xcdHlwZXNcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcmUgZGF0YSB0eXBlcyBmb3IgdGhlIERpZ2l0YWwgUm91dGluZSAmIFJlc3VsdHMgVHJhY2tlclxuXG5leHBvcnQgaW50ZXJmYWNlIFJvdXRpbmVJdGVtIHtcbiAgaWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBpY29uOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uPzogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIERhaWx5UHJvZ3Jlc3Mge1xuICB1c2VySWQ6IHN0cmluZztcbiAgZGF0ZTogc3RyaW5nOyAvLyBZWVlZLU1NLUREIGZvcm1hdFxuICBjb21wbGV0ZWRJdGVtczogc3RyaW5nW107IC8vIEFycmF5IG9mIHJvdXRpbmUgaXRlbSBJRHNcbiAgbGFzdFVwZGF0ZWQ6IHN0cmluZzsgLy8gSVNPIHRpbWVzdGFtcFxufVxuXG5leHBvcnQgaW50ZXJmYWNlIFVzZXIge1xuICBpZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGNyZWF0ZWRBdDogc3RyaW5nOyAvLyBJU08gdGltZXN0YW1wXG4gIGxhc3RBY3RpdmU6IHN0cmluZzsgLy8gSVNPIHRpbWVzdGFtcFxufVxuXG5leHBvcnQgaW50ZXJmYWNlIEhpc3RvcmljYWxEYXRhIHtcbiAgdXNlcklkOiBzdHJpbmc7XG4gIGRhdGU6IHN0cmluZzsgLy8gWVlZWS1NTS1ERCBmb3JtYXRcbiAgY29tcGxldGVkSXRlbXM6IHN0cmluZ1tdO1xuICBjb21wbGV0aW9uUmF0ZTogbnVtYmVyOyAvLyBQZXJjZW50YWdlICgwLTEwMClcbiAgc3RyZWFrOiBudW1iZXI7IC8vIERheXMgaW4gYSByb3cgd2l0aCBhY3Rpdml0eVxufVxuXG4vLyBEZWZhdWx0IHJvdXRpbmUgaXRlbXMgdGhhdCBldmVyeSB1c2VyIGdldHNcbmV4cG9ydCBjb25zdCBERUZBVUxUX1JPVVRJTkVfSVRFTVM6IFJvdXRpbmVJdGVtW10gPSBbXG4gIHtcbiAgICBpZDogJ3ByYXllcicsXG4gICAgbmFtZTogJ1ByYXllcicsXG4gICAgaWNvbjogJ/CfmY8nLFxuICAgIGRlc2NyaXB0aW9uOiAnRGFpbHkgc3Bpcml0dWFsIHByYWN0aWNlIGFuZCByZWZsZWN0aW9uJ1xuICB9LFxuICB7XG4gICAgaWQ6ICdzdHVkeScsXG4gICAgbmFtZTogJ1N0dWR5JyxcbiAgICBpY29uOiAn8J+TmicsXG4gICAgZGVzY3JpcHRpb246ICdMZWFybmluZywgcmVhZGluZywgb3Igc2tpbGwgZGV2ZWxvcG1lbnQnXG4gIH0sXG4gIHtcbiAgICBpZDogJ2h5Z2llbmUnLFxuICAgIG5hbWU6ICdIeWdpZW5lJyxcbiAgICBpY29uOiAn8J+nvCcsXG4gICAgZGVzY3JpcHRpb246ICdQZXJzb25hbCBjYXJlIGFuZCBjbGVhbmxpbmVzcydcbiAgfSxcbiAge1xuICAgIGlkOiAnd29yaycsXG4gICAgbmFtZTogJ1dvcmsnLFxuICAgIGljb246ICfwn5K8JyxcbiAgICBkZXNjcmlwdGlvbjogJ1Byb2Zlc3Npb25hbCB0YXNrcyBhbmQgcmVzcG9uc2liaWxpdGllcydcbiAgfSxcbiAge1xuICAgIGlkOiAnZXhlcmNpc2UnLFxuICAgIG5hbWU6ICdFeGVyY2lzZScsXG4gICAgaWNvbjogJ/CfkqonLFxuICAgIGRlc2NyaXB0aW9uOiAnUGh5c2ljYWwgYWN0aXZpdHkgYW5kIGZpdG5lc3MnXG4gIH0sXG4gIHtcbiAgICBpZDogJ251dHJpdGlvbicsXG4gICAgbmFtZTogJ051dHJpdGlvbicsXG4gICAgaWNvbjogJ/CfpZcnLFxuICAgIGRlc2NyaXB0aW9uOiAnSGVhbHRoeSBlYXRpbmcgYW5kIG1lYWwgcGxhbm5pbmcnXG4gIH0sXG4gIHtcbiAgICBpZDogJ3JlZmxlY3Rpb24nLFxuICAgIG5hbWU6ICdSZWZsZWN0aW9uJyxcbiAgICBpY29uOiAn8J+klCcsXG4gICAgZGVzY3JpcHRpb246ICdEYWlseSBqb3VybmFsaW5nIG9yIHNlbGYtcmVmbGVjdGlvbidcbiAgfSxcbiAge1xuICAgIGlkOiAnY29ubmVjdGlvbicsXG4gICAgbmFtZTogJ0Nvbm5lY3Rpb24nLFxuICAgIGljb246ICfwn5GlJyxcbiAgICBkZXNjcmlwdGlvbjogJ01lYW5pbmdmdWwgc29jaWFsIGludGVyYWN0aW9ucydcbiAgfVxuXTtcblxuLy8gVXRpbGl0eSBmdW5jdGlvbnMgZm9yIGRhdGUgaGFuZGxpbmdcbmV4cG9ydCBjb25zdCBnZXRDdXJyZW50RGF0ZSA9ICgpOiBzdHJpbmcgPT4ge1xuICByZXR1cm4gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF07XG59O1xuXG5leHBvcnQgY29uc3QgZ2V0Q3VycmVudFRpbWVzdGFtcCA9ICgpOiBzdHJpbmcgPT4ge1xuICByZXR1cm4gbmV3IERhdGUoKS50b0lTT1N0cmluZygpO1xufTtcblxuLy8gQ2hlY2sgaWYgaXQncyBhIG5ldyBkYXkgKGZvciByZXNldCBsb2dpYylcbmV4cG9ydCBjb25zdCBpc05ld0RheSA9IChsYXN0RGF0ZTogc3RyaW5nKTogYm9vbGVhbiA9PiB7XG4gIHJldHVybiBnZXRDdXJyZW50RGF0ZSgpICE9PSBsYXN0RGF0ZTtcbn07XG5cbi8vIENhbGN1bGF0ZSBjb21wbGV0aW9uIHJhdGVcbmV4cG9ydCBjb25zdCBjYWxjdWxhdGVDb21wbGV0aW9uUmF0ZSA9IChjb21wbGV0ZWQ6IHN0cmluZ1tdLCB0b3RhbDogbnVtYmVyKTogbnVtYmVyID0+IHtcbiAgcmV0dXJuIE1hdGgucm91bmQoKGNvbXBsZXRlZC5sZW5ndGggLyB0b3RhbCkgKiAxMDApO1xufTtcblxuLy8gR2VuZXJhdGUgdXNlciBJRCAoc2ltcGxlIGFwcHJvYWNoIGZvciBub3cpXG5leHBvcnQgY29uc3QgZ2VuZXJhdGVVc2VySWQgPSAobmFtZTogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgY29uc3QgdGltZXN0YW1wID0gRGF0ZS5ub3coKTtcbiAgY29uc3QgbmFtZUhhc2ggPSBuYW1lLnRvTG93ZXJDYXNlKCkucmVwbGFjZSgvXFxzKy9nLCAnLScpO1xuICByZXR1cm4gYCR7bmFtZUhhc2h9LSR7dGltZXN0YW1wfWA7XG59O1xuIl0sIm5hbWVzIjpbIkRFRkFVTFRfUk9VVElORV9JVEVNUyIsImlkIiwibmFtZSIsImljb24iLCJkZXNjcmlwdGlvbiIsImdldEN1cnJlbnREYXRlIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJnZXRDdXJyZW50VGltZXN0YW1wIiwiaXNOZXdEYXkiLCJsYXN0RGF0ZSIsImNhbGN1bGF0ZUNvbXBsZXRpb25SYXRlIiwiY29tcGxldGVkIiwidG90YWwiLCJNYXRoIiwicm91bmQiLCJsZW5ndGgiLCJnZW5lcmF0ZVVzZXJJZCIsInRpbWVzdGFtcCIsIm5vdyIsIm5hbWVIYXNoIiwidG9Mb3dlckNhc2UiLCJyZXBsYWNlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/storage.ts":
/*!******************************!*\
  !*** ./src/utils/storage.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateStreak: () => (/* binding */ calculateStreak),\n/* harmony export */   checkAndPerformDailyReset: () => (/* binding */ checkAndPerformDailyReset),\n/* harmony export */   clearAllData: () => (/* binding */ clearAllData),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getDailyProgress: () => (/* binding */ getDailyProgress),\n/* harmony export */   getHistoricalData: () => (/* binding */ getHistoricalData),\n/* harmony export */   getTodayProgress: () => (/* binding */ getTodayProgress),\n/* harmony export */   getUserByName: () => (/* binding */ getUserByName),\n/* harmony export */   getUserHistoricalData: () => (/* binding */ getUserHistoricalData),\n/* harmony export */   getUserProgress: () => (/* binding */ getUserProgress),\n/* harmony export */   getUsers: () => (/* binding */ getUsers),\n/* harmony export */   saveDailyProgress: () => (/* binding */ saveDailyProgress),\n/* harmony export */   saveHistoricalData: () => (/* binding */ saveHistoricalData),\n/* harmony export */   saveUser: () => (/* binding */ saveUser),\n/* harmony export */   setCurrentUser: () => (/* binding */ setCurrentUser)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n// Local storage utilities for the Digital Routine & Results Tracker\n\nconst STORAGE_KEYS = {\n    USERS: 'routine_tracker_users',\n    DAILY_PROGRESS: 'routine_tracker_daily_progress',\n    HISTORICAL_DATA: 'routine_tracker_historical_data',\n    CURRENT_USER: 'routine_tracker_current_user'\n};\n// User management\nconst saveUser = (user)=>{\n    if (false) {}\n    const users = getUsers();\n    const existingIndex = users.findIndex((u)=>u.id === user.id);\n    if (existingIndex >= 0) {\n        users[existingIndex] = user;\n    } else {\n        users.push(user);\n    }\n    localStorage.setItem(STORAGE_KEYS.USERS, JSON.stringify(users));\n};\nconst getUsers = ()=>{\n    if (false) {}\n    const stored = localStorage.getItem(STORAGE_KEYS.USERS);\n    return stored ? JSON.parse(stored) : [];\n};\nconst getUserByName = (name)=>{\n    const users = getUsers();\n    return users.find((u)=>u.name.toLowerCase() === name.toLowerCase()) || null;\n};\nconst setCurrentUser = (userId)=>{\n    if (false) {}\n    localStorage.setItem(STORAGE_KEYS.CURRENT_USER, userId);\n};\nconst getCurrentUser = ()=>{\n    if (false) {}\n    return localStorage.getItem(STORAGE_KEYS.CURRENT_USER);\n};\n// Daily progress management\nconst saveDailyProgress = (progress)=>{\n    if (false) {}\n    const allProgress = getDailyProgress();\n    const key = \"\".concat(progress.userId, \"_\").concat(progress.date);\n    allProgress[key] = progress;\n    localStorage.setItem(STORAGE_KEYS.DAILY_PROGRESS, JSON.stringify(allProgress));\n};\nconst getDailyProgress = ()=>{\n    if (false) {}\n    const stored = localStorage.getItem(STORAGE_KEYS.DAILY_PROGRESS);\n    return stored ? JSON.parse(stored) : {};\n};\nconst getTodayProgress = (userId)=>{\n    const allProgress = getDailyProgress();\n    const key = \"\".concat(userId, \"_\").concat((0,_types__WEBPACK_IMPORTED_MODULE_0__.getCurrentDate)());\n    return allProgress[key] || null;\n};\nconst getUserProgress = (userId, date)=>{\n    const allProgress = getDailyProgress();\n    const key = \"\".concat(userId, \"_\").concat(date);\n    return allProgress[key] || null;\n};\n// Historical data management\nconst saveHistoricalData = (data)=>{\n    if (false) {}\n    const allHistorical = getHistoricalData();\n    const key = \"\".concat(data.userId, \"_\").concat(data.date);\n    allHistorical[key] = data;\n    localStorage.setItem(STORAGE_KEYS.HISTORICAL_DATA, JSON.stringify(allHistorical));\n};\nconst getHistoricalData = ()=>{\n    if (false) {}\n    const stored = localStorage.getItem(STORAGE_KEYS.HISTORICAL_DATA);\n    return stored ? JSON.parse(stored) : {};\n};\nconst getUserHistoricalData = function(userId) {\n    let days = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 30;\n    const allHistorical = getHistoricalData();\n    const userHistorical = [];\n    // Get last N days of data\n    for(let i = 0; i < days; i++){\n        const date = new Date();\n        date.setDate(date.getDate() - i);\n        const dateStr = date.toISOString().split('T')[0];\n        const key = \"\".concat(userId, \"_\").concat(dateStr);\n        if (allHistorical[key]) {\n            userHistorical.push(allHistorical[key]);\n        }\n    }\n    return userHistorical.sort((a, b)=>new Date(a.date).getTime() - new Date(b.date).getTime());\n};\n// Daily reset logic\nconst checkAndPerformDailyReset = (userId)=>{\n    const todayProgress = getTodayProgress(userId);\n    const currentDate = (0,_types__WEBPACK_IMPORTED_MODULE_0__.getCurrentDate)();\n    // If no progress for today, check if we need to archive yesterday's data\n    if (!todayProgress) {\n        const yesterday = new Date();\n        yesterday.setDate(yesterday.getDate() - 1);\n        const yesterdayStr = yesterday.toISOString().split('T')[0];\n        const yesterdayProgress = getUserProgress(userId, yesterdayStr);\n        if (yesterdayProgress) {\n            // Archive yesterday's progress to historical data\n            const historicalData = {\n                userId: yesterdayProgress.userId,\n                date: yesterdayProgress.date,\n                completedItems: yesterdayProgress.completedItems,\n                completionRate: Math.round(yesterdayProgress.completedItems.length / 8 * 100),\n                streak: calculateStreak(userId, yesterdayStr)\n            };\n            saveHistoricalData(historicalData);\n        }\n        return true; // New day, reset needed\n    }\n    return false; // Same day, no reset needed\n};\n// Calculate current streak\nconst calculateStreak = (userId, endDate)=>{\n    let streak = 0;\n    const date = new Date(endDate);\n    while(true){\n        const dateStr = date.toISOString().split('T')[0];\n        const progress = getUserProgress(userId, dateStr);\n        if (progress && progress.completedItems.length > 0) {\n            streak++;\n            date.setDate(date.getDate() - 1);\n        } else {\n            break;\n        }\n    }\n    return streak;\n};\n// Clear all data (for testing/reset)\nconst clearAllData = ()=>{\n    if (false) {}\n    Object.values(STORAGE_KEYS).forEach((key)=>{\n        localStorage.removeItem(key);\n    });\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/storage.ts\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CUSER%5C%5CDesktop%5C%5Carchive-2025-06-05T142720%2B0200%5C%5CPractice%5C%5CRr%201.0%5C%5Croutine-tracker%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);