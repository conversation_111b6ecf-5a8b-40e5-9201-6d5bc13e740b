"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/types/index.ts":
/*!****************************!*\
  !*** ./src/types/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_ROUTINE_ITEMS: () => (/* binding */ DEFAULT_ROUTINE_ITEMS),\n/* harmony export */   calculateCompletionRate: () => (/* binding */ calculateCompletionRate),\n/* harmony export */   generateUserId: () => (/* binding */ generateUserId),\n/* harmony export */   getCurrentDate: () => (/* binding */ getCurrentDate),\n/* harmony export */   getCurrentTimestamp: () => (/* binding */ getCurrentTimestamp),\n/* harmony export */   isNewDay: () => (/* binding */ isNewDay)\n/* harmony export */ });\n// Core data types for the Digital Routine & Results Tracker\n// Default routine items that every user gets\nconst DEFAULT_ROUTINE_ITEMS = [\n    {\n        id: 'prayer',\n        name: 'Prayer',\n        icon: '🙏',\n        description: 'Daily spiritual practice and reflection',\n        detailedInfo: {\n            purpose: 'Connect with your spiritual side and find inner peace through daily prayer and meditation.',\n            benefits: [\n                'Reduces stress and anxiety',\n                'Provides mental clarity and focus',\n                'Strengthens spiritual connection',\n                'Promotes gratitude and mindfulness'\n            ],\n            tips: [\n                'Set aside a quiet time each day',\n                'Find a comfortable, peaceful space',\n                'Start with just 5-10 minutes if you\\'re new',\n                'Use prayer beads or meditation apps if helpful'\n            ],\n            timeRecommendation: '10-30 minutes',\n            frequency: 'Daily, preferably same time each day'\n        }\n    },\n    {\n        id: 'study',\n        name: 'Study',\n        icon: '📚',\n        description: 'Learning, reading, or skill development',\n        detailedInfo: {\n            purpose: 'Continuous learning and personal development through reading, courses, or skill practice.',\n            benefits: [\n                'Expands knowledge and skills',\n                'Improves cognitive function',\n                'Enhances career prospects',\n                'Builds confidence and competence'\n            ],\n            tips: [\n                'Choose topics that interest and challenge you',\n                'Set specific learning goals',\n                'Take notes and review regularly',\n                'Apply what you learn in real situations'\n            ],\n            timeRecommendation: '30-60 minutes',\n            frequency: 'Daily, consistent schedule works best'\n        }\n    },\n    {\n        id: 'hygiene',\n        name: 'Hygiene',\n        icon: '🧼',\n        description: 'Personal care and cleanliness',\n        detailedInfo: {\n            purpose: 'Maintain physical health and personal presentation through proper hygiene practices.',\n            benefits: [\n                'Prevents illness and infections',\n                'Boosts self-confidence',\n                'Shows respect for others',\n                'Maintains professional appearance'\n            ],\n            tips: [\n                'Brush teeth twice daily',\n                'Shower regularly and use deodorant',\n                'Keep nails clean and trimmed',\n                'Maintain clean, appropriate clothing'\n            ],\n            timeRecommendation: '20-45 minutes total',\n            frequency: 'Multiple times daily as needed'\n        }\n    },\n    {\n        id: 'work',\n        name: 'Work',\n        icon: '💼',\n        description: 'Professional tasks and responsibilities',\n        detailedInfo: {\n            purpose: 'Focus on productive work activities that contribute to your career and financial goals.',\n            benefits: [\n                'Builds career advancement',\n                'Provides financial stability',\n                'Develops professional skills',\n                'Creates sense of accomplishment'\n            ],\n            tips: [\n                'Prioritize important tasks first',\n                'Take regular breaks to maintain focus',\n                'Set clear daily and weekly goals',\n                'Minimize distractions during work time'\n            ],\n            timeRecommendation: '6-8 hours',\n            frequency: 'Daily during work days'\n        }\n    },\n    {\n        id: 'exercise',\n        name: 'Exercise',\n        icon: '💪',\n        description: 'Physical activity and fitness',\n        detailedInfo: {\n            purpose: 'Maintain physical health and fitness through regular exercise and movement.',\n            benefits: [\n                'Improves cardiovascular health',\n                'Builds strength and endurance',\n                'Enhances mood and energy',\n                'Helps maintain healthy weight'\n            ],\n            tips: [\n                'Start with activities you enjoy',\n                'Begin slowly and gradually increase intensity',\n                'Mix cardio, strength, and flexibility training',\n                'Stay hydrated and listen to your body'\n            ],\n            timeRecommendation: '30-60 minutes',\n            frequency: 'Daily or at least 5 times per week'\n        }\n    },\n    {\n        id: 'nutrition',\n        name: 'Nutrition',\n        icon: '🥗',\n        description: 'Healthy eating and meal planning',\n        detailedInfo: {\n            purpose: 'Nourish your body with healthy, balanced meals and proper nutrition.',\n            benefits: [\n                'Provides sustained energy',\n                'Supports immune system',\n                'Improves mental clarity',\n                'Maintains healthy weight'\n            ],\n            tips: [\n                'Plan meals in advance',\n                'Include variety of fruits and vegetables',\n                'Stay hydrated throughout the day',\n                'Practice portion control and mindful eating'\n            ],\n            timeRecommendation: '15-30 minutes per meal',\n            frequency: '3 main meals + healthy snacks'\n        }\n    },\n    {\n        id: 'reflection',\n        name: 'Reflection',\n        icon: '🤔',\n        description: 'Daily journaling or self-reflection',\n        detailedInfo: {\n            purpose: 'Take time to reflect on your day, thoughts, and personal growth journey.',\n            benefits: [\n                'Increases self-awareness',\n                'Helps process emotions',\n                'Tracks personal growth',\n                'Improves decision-making'\n            ],\n            tips: [\n                'Write about your day\\'s highlights and challenges',\n                'Ask yourself what you learned today',\n                'Set intentions for tomorrow',\n                'Be honest and non-judgmental with yourself'\n            ],\n            timeRecommendation: '10-20 minutes',\n            frequency: 'Daily, preferably evening'\n        }\n    },\n    {\n        id: 'connection',\n        name: 'Connection',\n        icon: '👥',\n        description: 'Meaningful social interactions',\n        detailedInfo: {\n            purpose: 'Build and maintain meaningful relationships with family, friends, and community.',\n            benefits: [\n                'Reduces feelings of loneliness',\n                'Provides emotional support',\n                'Enhances sense of belonging',\n                'Improves mental health'\n            ],\n            tips: [\n                'Reach out to someone you care about',\n                'Practice active listening',\n                'Share your thoughts and feelings openly',\n                'Make time for quality conversations'\n            ],\n            timeRecommendation: '30-60 minutes',\n            frequency: 'Daily interactions, deeper connections weekly'\n        }\n    }\n];\n// Utility functions for date handling\nconst getCurrentDate = ()=>{\n    return new Date().toISOString().split('T')[0];\n};\nconst getCurrentTimestamp = ()=>{\n    return new Date().toISOString();\n};\n// Check if it's a new day (for reset logic)\nconst isNewDay = (lastDate)=>{\n    return getCurrentDate() !== lastDate;\n};\n// Calculate completion rate\nconst calculateCompletionRate = (completed, total)=>{\n    return Math.round(completed.length / total * 100);\n};\n// Generate user ID (simple approach for now)\nconst generateUserId = (name)=>{\n    const timestamp = Date.now();\n    const nameHash = name.toLowerCase().replace(/\\s+/g, '-');\n    return \"\".concat(nameHash, \"-\").concat(timestamp);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/types/index.ts\n"));

/***/ })

});