// Authorized users configuration
// Add names of people who should have access to the tracker

export interface AuthorizedUser {
  name: string;
  displayName?: string; // Optional custom display name
  role?: 'admin' | 'user'; // Optional role for future features
  joinDate?: string; // When they were added
}

// List of authorized users - add names here to grant access
export const AUTHORIZED_USERS: AuthorizedUser[] = [
  {
    name: "<PERSON><PERSON><PERSON>", // Names are case-insensitive
    displayName: "<PERSON><PERSON><PERSON>",
    role: "admin",
    joinDate: "2025-01-10"
  },
  {
    name: "<PERSON>",
    displayName: "<PERSON>", 
    role: "user",
    joinDate: "2025-01-10"
  },
  // Add more authorized users here
  // {
  //   name: "your name here",
  //   displayName: "Your Display Name",
  //   role: "user",
  //   joinDate: "2025-01-10"
  // },
];

// Removed invitation codes - using name-based access only

// Utility functions
export const isUserAuthorized = (inputName: string): AuthorizedUser | null => {
  const normalizedInput = inputName.toLowerCase().trim();

  // Check against authorized names only
  const authorizedUser = AUTHORIZED_USERS.find(
    user => user.name.toLowerCase() === normalizedInput
  );

  return authorizedUser || null;
};

export const getDisplayName = (user: AuthorizedUser, inputName: string): string => {
  return user.displayName || inputName;
};

// Admin functions (for future use)
export const isAdmin = (user: AuthorizedUser): boolean => {
  return user.role === 'admin';
};

export const getTotalAuthorizedUsers = (): number => {
  return AUTHORIZED_USERS.length;
};
