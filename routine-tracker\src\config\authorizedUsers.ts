// Authorized users configuration
// Add names of people who should have access to the tracker

export interface AuthorizedUser {
  name: string;
  displayName?: string; // Optional custom display name
  role?: 'admin' | 'user'; // Optional role for future features
  joinDate?: string; // When they were added
}

// List of authorized users - add names here to grant access
export const AUTHORIZED_USERS: AuthorizedUser[] = [
  {
    name: "john doe", // Names are case-insensitive
    displayName: "<PERSON>",
    role: "admin",
    joinDate: "2025-01-10"
  },
  {
    name: "jane smith",
    displayName: "<PERSON>", 
    role: "user",
    joinDate: "2025-01-10"
  },
  {
    name: "alex johnson",
    displayName: "<PERSON>",
    role: "user", 
    joinDate: "2025-01-10"
  },
  // Add more authorized users here
  // {
  //   name: "your name here",
  //   displayName: "Your Display Name",
  //   role: "user",
  //   joinDate: "2025-01-10"
  // },
];

// Alternative: Use invitation codes instead of names
export const INVITATION_CODES: Record<string, AuthorizedUser> = {
  "GROWTH2025": {
    name: "growth_member",
    displayName: "Growth Member",
    role: "user",
    joinDate: "2025-01-10"
  },
  "TECHTALK": {
    name: "tech_talk_member", 
    displayName: "Tech Talk Member",
    role: "user",
    joinDate: "2025-01-10"
  },
  "ADMIN123": {
    name: "admin_user",
    displayName: "Admin User", 
    role: "admin",
    joinDate: "2025-01-10"
  }
};

// Utility functions
export const isUserAuthorized = (inputName: string): AuthorizedUser | null => {
  const normalizedInput = inputName.toLowerCase().trim();
  
  // Check against authorized names
  const authorizedUser = AUTHORIZED_USERS.find(
    user => user.name.toLowerCase() === normalizedInput
  );
  
  if (authorizedUser) {
    return authorizedUser;
  }
  
  // Check against invitation codes
  const inviteCode = INVITATION_CODES[inputName.toUpperCase().trim()];
  if (inviteCode) {
    return inviteCode;
  }
  
  return null;
};

export const getDisplayName = (user: AuthorizedUser, inputName: string): string => {
  return user.displayName || inputName;
};

// Admin functions (for future use)
export const isAdmin = (user: AuthorizedUser): boolean => {
  return user.role === 'admin';
};

export const getTotalAuthorizedUsers = (): number => {
  return AUTHORIZED_USERS.length + Object.keys(INVITATION_CODES).length;
};
