"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/config/authorizedUsers.ts":
/*!***************************************!*\
  !*** ./src/config/authorizedUsers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTHORIZED_USERS: () => (/* binding */ AUTHORIZED_USERS),\n/* harmony export */   getDisplayName: () => (/* binding */ getDisplayName),\n/* harmony export */   getTotalAuthorizedUsers: () => (/* binding */ getTotalAuthorizedUsers),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   isUserAuthorized: () => (/* binding */ isUserAuthorized)\n/* harmony export */ });\n// Authorized users configuration\n// Add names of people who should have access to the tracker\n// List of authorized users - add names here to grant access\nconst AUTHORIZED_USERS = [\n    {\n        name: \"Daniel\",\n        displayName: \"Daniel\",\n        role: \"admin\",\n        joinDate: \"2025-01-10\"\n    },\n    {\n        name: \"jane smith\",\n        displayName: \"Jane Smith\",\n        role: \"user\",\n        joinDate: \"2025-01-10\"\n    },\n    {\n        name: \"alex johnson\",\n        displayName: \"Alex Johnson\",\n        role: \"user\",\n        joinDate: \"2025-01-10\"\n    }\n];\n// Removed invitation codes - using name-based access only\n// Utility functions\nconst isUserAuthorized = (inputName)=>{\n    const normalizedInput = inputName.toLowerCase().trim();\n    // Check against authorized names\n    const authorizedUser = AUTHORIZED_USERS.find((user)=>user.name.toLowerCase() === normalizedInput);\n    if (authorizedUser) {\n        return authorizedUser;\n    }\n    // Check against invitation codes\n    const inviteCode = INVITATION_CODES[inputName.toUpperCase().trim()];\n    if (inviteCode) {\n        return inviteCode;\n    }\n    return null;\n};\nconst getDisplayName = (user, inputName)=>{\n    return user.displayName || inputName;\n};\n// Admin functions (for future use)\nconst isAdmin = (user)=>{\n    return user.role === 'admin';\n};\nconst getTotalAuthorizedUsers = ()=>{\n    return AUTHORIZED_USERS.length + Object.keys(INVITATION_CODES).length;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/authorizedUsers.ts\n"));

/***/ })

});