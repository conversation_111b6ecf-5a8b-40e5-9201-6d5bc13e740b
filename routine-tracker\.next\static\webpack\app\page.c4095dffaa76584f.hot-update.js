"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/WelcomeScreen.tsx":
/*!******************************************!*\
  !*** ./src/components/WelcomeScreen.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WelcomeScreen: () => (/* binding */ WelcomeScreen)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/types */ \"(app-pages-browser)/./src/types/index.ts\");\n/* harmony import */ var _utils_storage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/storage */ \"(app-pages-browser)/./src/utils/storage.ts\");\n/* harmony import */ var _config_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/auth */ \"(app-pages-browser)/./src/config/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ WelcomeScreen auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction WelcomeScreen(param) {\n    let { onUserLogin } = param;\n    _s();\n    const [name, setName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [authError, setAuthError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [showSuccess, setShowSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!name.trim()) return;\n        setIsLoading(true);\n        setAuthError('');\n        try {\n            // Check if user is authorized\n            if (!(0,_config_auth__WEBPACK_IMPORTED_MODULE_4__.isUserAuthorized)(name.trim())) {\n                setAuthError('Access denied. Your name is not on the authorized list.');\n                setIsLoading(false);\n                return;\n            }\n            // Get the proper display name for authorized user\n            const displayName = (0,_config_auth__WEBPACK_IMPORTED_MODULE_4__.getUserDisplayName)(name.trim());\n            // Check if user already exists\n            let user = (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.getUserByName)(displayName);\n            if (!user) {\n                // Create new user with display name\n                user = {\n                    id: (0,_types__WEBPACK_IMPORTED_MODULE_2__.generateUserId)(displayName),\n                    name: displayName,\n                    createdAt: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)(),\n                    lastActive: (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)()\n                };\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveUser)(user);\n            } else {\n                // Update last active time\n                user.lastActive = (0,_types__WEBPACK_IMPORTED_MODULE_2__.getCurrentTimestamp)();\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.saveUser)(user);\n            }\n            // Show success message\n            setShowSuccess(true);\n            // Set as current user after a brief delay for better UX\n            setTimeout(()=>{\n                (0,_utils_storage__WEBPACK_IMPORTED_MODULE_3__.setCurrentUser)(user.id);\n                onUserLogin(user.id);\n            }, 1000);\n        } catch (error) {\n            console.error('Error logging in:', error);\n            setAuthError('An error occurred. Please try again.');\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-6xl mb-4\",\n                            children: \"\\uD83E\\uDDF1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"Digital Routine & Results Tracker\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 text-lg\",\n                            children: \"Track your daily growth with intention\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6 mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold text-gray-800 mb-4\",\n                            children: \"What we're building here \\uD83D\\uDCBB✅\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Type in your name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 91,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Tick what you've done for the day (Prayer, Study, Hygiene, Work, etc.)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 95,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Submit, and it saves your progress\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Come back anytime before the day ends to update it\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-indigo-500 mt-1\",\n                                            children: \"•\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"System resets at midnight, but keeps a history of your growth\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold text-gray-800 mb-4\",\n                            children: \"Ready to track your growth? \\uD83D\\uDCCA✨\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"name\",\n                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                            children: \"Your Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            id: \"name\",\n                                            value: name,\n                                            onChange: (e)=>setName(e.target.value),\n                                            placeholder: \"Enter your name...\",\n                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 outline-none transition-colors\",\n                                            disabled: isLoading,\n                                            required: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isLoading || !name.trim(),\n                                    className: \"w-full bg-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-indigo-700 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Getting started...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 17\n                                    }, this) : 'Start Tracking 🔥'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mt-6 text-gray-500 text-sm\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"No login stress. No judgment. Just you, your goals, and your growth.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\archive-2025-06-05T142720+0200\\\\Practice\\\\Rr 1.0\\\\routine-tracker\\\\src\\\\components\\\\WelcomeScreen.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, this);\n}\n_s(WelcomeScreen, \"Q1I2hcr/euPGQ2ZuLkrJkZrIaZ8=\");\n_c = WelcomeScreen;\nvar _c;\n$RefreshReg$(_c, \"WelcomeScreen\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WelcomeScreen.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/config/auth.ts":
/*!****************************!*\
  !*** ./src/config/auth.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AUTHORIZED_USERS: () => (/* binding */ AUTHORIZED_USERS),\n/* harmony export */   getAllAuthorizedNames: () => (/* binding */ getAllAuthorizedNames),\n/* harmony export */   getAuthorizedUser: () => (/* binding */ getAuthorizedUser),\n/* harmony export */   getAuthorizedUsersCount: () => (/* binding */ getAuthorizedUsersCount),\n/* harmony export */   getUserDisplayName: () => (/* binding */ getUserDisplayName),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   isUserAuthorized: () => (/* binding */ isUserAuthorized)\n/* harmony export */ });\n// Authorized users configuration for the Digital Routine & Results Tracker\n// Only users with names in this list can access the application\n// List of authorized users - Add names here to grant access\nconst AUTHORIZED_USERS = [\n    {\n        name: \"john doe\",\n        displayName: \"John Doe\",\n        role: \"admin\",\n        joinDate: \"2025-01-10\"\n    },\n    {\n        name: \"jane smith\",\n        displayName: \"Jane Smith\",\n        role: \"user\",\n        joinDate: \"2025-01-10\"\n    },\n    {\n        name: \"alex johnson\",\n        displayName: \"Alex Johnson\",\n        role: \"user\",\n        joinDate: \"2025-01-10\"\n    },\n    {\n        name: \"sarah wilson\",\n        displayName: \"Sarah Wilson\",\n        role: \"user\",\n        joinDate: \"2025-01-10\"\n    },\n    {\n        name: \"mike brown\",\n        displayName: \"Mike Brown\",\n        role: \"user\",\n        joinDate: \"2025-01-10\"\n    }\n];\n// Utility functions for user authorization\nconst isUserAuthorized = (inputName)=>{\n    const normalizedInput = inputName.toLowerCase().trim();\n    return AUTHORIZED_USERS.some((user)=>user.name.toLowerCase() === normalizedInput);\n};\nconst getAuthorizedUser = (inputName)=>{\n    const normalizedInput = inputName.toLowerCase().trim();\n    return AUTHORIZED_USERS.find((user)=>user.name.toLowerCase() === normalizedInput) || null;\n};\nconst getUserDisplayName = (inputName)=>{\n    const user = getAuthorizedUser(inputName);\n    return (user === null || user === void 0 ? void 0 : user.displayName) || inputName;\n};\nconst isAdmin = (inputName)=>{\n    const user = getAuthorizedUser(inputName);\n    return (user === null || user === void 0 ? void 0 : user.role) === 'admin';\n};\n// Get total number of authorized users\nconst getAuthorizedUsersCount = ()=>{\n    return AUTHORIZED_USERS.length;\n};\n// Get all authorized user names (for admin purposes)\nconst getAllAuthorizedNames = ()=>{\n    return AUTHORIZED_USERS.map((user)=>user.displayName || user.name);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/config/auth.ts\n"));

/***/ })

});